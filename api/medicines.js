// 药品表 API
const { API_BASE } = require('./base.js');
const BASE_URL = `${API_BASE}/medicines/medicines`;

function withAuth(options = {}) {
  const token = wx.getStorageSync('token');
  if (!token) {
    return Promise.reject('未登录');
  }
  options.header = Object.assign({}, options.header, {
    'Authorization': 'Bearer ' + token
  });
  return options;
}

function getMedicines(params) {
  return new Promise((resolve, reject) => {
    wx.request(Object.assign(withAuth({
      url: BASE_URL,
      method: 'GET',
      data: params,
      success: resolve,
      fail: reject
    })));
  });
}

function getMedicineDetail(medicine_id) {
  return new Promise((resolve, reject) => {
    // 验证medicine_id参数
    if (!medicine_id || medicine_id === 'medicines' || isNaN(Number(medicine_id))) {
      reject(new Error('无效的药品ID'));
      return;
    }
    
    wx.request(Object.assign(withAuth({
      url: `${BASE_URL}/${medicine_id}`,
      method: 'GET',
      success: resolve,
      fail: reject
    })));
  });
}

function createMedicine(data) {
  return new Promise((resolve, reject) => {
    wx.request(Object.assign(withAuth({
      url: BASE_URL,
      method: 'POST',
      data,
      success: resolve,
      fail: reject
    })));
  });
}

function updateMedicine(medicine_id, data) {
  return new Promise((resolve, reject) => {
    // 验证medicine_id参数
    if (!medicine_id || medicine_id === 'medicines' || isNaN(Number(medicine_id))) {
      reject(new Error('无效的药品ID'));
      return;
    }
    
    wx.request(Object.assign(withAuth({
      url: `${BASE_URL}/${medicine_id}`,
      method: 'PUT',
      data,
      success: resolve,
      fail: reject
    })));
  });
}

function deleteMedicine(medicine_id) {
  return new Promise((resolve, reject) => {
    // 验证medicine_id参数
    if (!medicine_id || medicine_id === 'medicines' || isNaN(Number(medicine_id))) {
      reject(new Error('无效的药品ID'));
      return;
    }
    
    wx.request(Object.assign(withAuth({
      url: `${BASE_URL}/${medicine_id}`,
      method: 'DELETE',
      success: resolve,
      fail: reject
    })));
  });
}

export function getMedicineList(params = { pageSize: 100 }) {
  return new Promise((resolve, reject) => {
    wx.request({
      url: BASE_URL,
      method: 'GET',
      data: params,
      header: {
        'Authorization': 'Bearer ' + wx.getStorageSync('token'),
        'Content-Type': 'application/json'
      },
      success: (res) => resolve(res.data),
      fail: reject
    });
  });
}

// 其他API方法如有，也请用export function ...方式导出 