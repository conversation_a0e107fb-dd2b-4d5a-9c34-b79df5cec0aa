// 陪诊日记表 API
const { API_BASE } = require('./base.js');
const { withAuth } = require('../utils/auth.js');

function getDiaries(params) {
  return new Promise((resolve, reject) => {
    // 获取当前用户信息，确保权限验证
    const userInfo = wx.getStorageSync('userInfo') || {};
    const userType = userInfo.user_type;
    
    console.log('[getDiaries] 调用参数:', params);
    console.log('[getDiaries] 用户信息:', userInfo);
    console.log('[getDiaries] 用户类型:', userType);
    
    // 确保用户只能查询自己的日记或公开日记
    if (userType === 'patient' && !params.userId && !params.privacyLevel) {
      // 默认查询自己的日记
      params.userId = userInfo.id;
      console.log('[getDiaries] 病患用户，设置userId:', userInfo.id);
    }
    
    // 医护人员可以查看自己的日记或公开日记
    if ((userType === 'staff' || userType === 'medical_staff') && !params.userId && !params.privacyLevel) {
      params.userId = userInfo.id;
      console.log('[getDiaries] 医护人员，设置userId:', userInfo.id);
    }
    
    // 设置limit为1000，避免分页问题
    if (!params.limit) {
      params.limit = 1000;
    }
    
    console.log('[getDiaries] 最终请求参数:', params);
    
    wx.request(Object.assign(withAuth({
      url: `${API_BASE}/social/diaries`,
      method: 'GET',
      data: params,
      header: { 'content-type': 'application/json' },
      success: (res) => {
        console.log('[getDiaries] API响应:', res);
        if (res.data && res.data.data && res.data.data.diaries) {
          console.log('[getDiaries] 日记数量:', res.data.data.diaries.length);
          resolve({
            diaries: res.data.data.diaries,
            pagination: res.data.data.pagination
          });
        } else {
          console.log('[getDiaries] 响应格式异常:', res.data);
          resolve(res.data);
        }
      },
      fail: (error) => {
        console.error('[getDiaries] 请求失败:', error);
        reject(error);
      }
    })));
  });
}

function getDiaryDetail(diary_id) {
  return new Promise((resolve, reject) => {
    wx.request(Object.assign(withAuth({
      url: `${API_BASE}/social/diaries/${diary_id}`,
      method: 'GET',
      header: { 'content-type': 'application/json' },
      success: (res) => {
        if (res.data && res.data.data) {
          resolve(res.data.data);
        } else {
          resolve(res.data);
        }
      },
      fail: reject
    })));
  });
}

function createDiary(data) {
  return new Promise((resolve, reject) => {
    wx.request(Object.assign(withAuth({
      url: `${API_BASE}/social/diaries`,
      method: 'POST',
      data,
      header: { 'content-type': 'application/json' },
      success: res => resolve(res.data),
      fail: reject
    })));
  });
}

function updateDiary(diary_id, data) {
  return new Promise((resolve, reject) => {
    wx.request(Object.assign(withAuth({
      url: `${API_BASE}/social/diaries/${diary_id}`,
      method: 'PUT',
      data,
      header: { 'content-type': 'application/json' },
      success: res => resolve(res.data),
      fail: reject
    })));
  });
}

function deleteDiary(diary_id) {
  return new Promise((resolve, reject) => {
    wx.request(Object.assign(withAuth({
      url: `${API_BASE}/social/diaries/${diary_id}`,
      method: 'DELETE',
      header: { 'content-type': 'application/json' },
      success: res => resolve(res.data),
      fail: reject
    })));
  });
}

// 新增：日记图片上传，实际调用 /social/diaries/:id/images
function uploadDiaryImage(diaryId, filePath) {
  const token = wx.getStorageSync('token');
  return new Promise((resolve, reject) => {
    wx.uploadFile({
      url: `${API_BASE}/social/diaries/${diaryId}/images`, // 必须是这个路径
      filePath,
      name: 'images',
      header: {
        'Authorization': token ? 'Bearer ' + token : ''
      },
      success: (res) => {
        try {
          const data = JSON.parse(res.data);
          if (data.success && data.data && data.data.uploadedImages && data.data.uploadedImages.length) {
            resolve(data.data.uploadedImages[0]);
          } else {
            resolve(null);
          }
        } catch (e) { resolve(null); }
      },
      fail: reject
    });
  });
}

function uploadDiaryImages(fileList) {
  const token = wx.getStorageSync('token');
  return new Promise((resolve, reject) => {
    // H5/PC 端
    if (typeof window !== 'undefined' && window.FormData) {
      const formData = new FormData();
      for (const file of fileList) {
        formData.append('images', file);
      }
      fetch(`${API_BASE}/social/diaries/upload_image`, {
        method: 'POST',
        headers: {
          'Authorization': token ? 'Bearer ' + token : ''
        },
        body: formData
      })
        .then(res => res.json())
        .then(data => {
          if (data.success && data.data && data.data.uploadedImages) {
            resolve(data.data.uploadedImages);
          } else {
            reject(data.error || '上传失败');
          }
        })
        .catch(reject);
    } else {
      // 小程序端
      const uploaded = [];
      let count = 0;
      if (!fileList || !fileList.length) return resolve([]);
      fileList.forEach((filePath, idx) => {
        wx.uploadFile({
          url: `${API_BASE}/social/diaries/upload_image`,
          filePath,
          name: 'images',
          header: {
            'Authorization': token ? 'Bearer ' + token : ''
          },
          success: (res) => {
            try {
              const data = JSON.parse(res.data);
              if (data.success && data.data && data.data.uploadedImages && data.data.uploadedImages.length) {
                uploaded.push(...data.data.uploadedImages);
              }
            } catch (e) {}
            count++;
            if (count === fileList.length) resolve(uploaded);
          },
          fail: () => {
            count++;
            if (count === fileList.length) resolve(uploaded);
          }
        });
      });
    }
  });
}

module.exports = {
  getDiaries,
  getDiaryDetail,
  createDiary,
  updateDiary,
  deleteDiary,
  uploadDiaryImage,
  uploadDiaryImages
}; 