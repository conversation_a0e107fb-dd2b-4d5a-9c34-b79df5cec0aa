// 服务订单表 API
import { API_BASE } from './base.js';
const BASE_URL = `${API_BASE}/orders/service_orders`;
const { withAuth } = require('../utils/auth.js');

function getOrders(params) {
  return new Promise((resolve, reject) => {
    // 获取当前用户信息，确保权限验证
    const userInfo = wx.getStorageSync('userInfo') || {};
    const userType = userInfo.user_type;
    
    // 确保病患用户只能查询自己的订单
    if (userType === 'patient' && !params.userId) {
      params.userId = userInfo.id;
    }
    
    // 确保医护人员只能查询分配给自己的订单
    if ((userType === 'staff' || userType === 'medical_staff') && !params.staffId) {
      params.staffId = userInfo.staffId;
    }
    
    wx.request(Object.assign(withAuth({
      url: BASE_URL,
      method: 'GET',
      data: params,
      success: (res) => {
        // 兼容后端返回结构，直接返回 orders 和 pagination
        if (res.data && res.data.data && res.data.data.orders) {
          resolve({
            orders: res.data.data.orders,
            pagination: res.data.data.pagination
          });
        } else {
          resolve(res.data);
        }
      },
      fail: reject
    })));
  });
}

/**
 * 获取服务订单详情（统一返回完整响应对象）
 * @param {number|string} order_id
 * @returns {Promise<{success: boolean, data: object, [msg]: string}>}
 * 
 * 【全局调用规范】
 *   - 统一判断：if (res.success && res.data) { // 使用res.data }
 *   - 若res.success为false或无data，视为无效/不存在/异常
 */
function getOrderDetail(order_id) {
  return new Promise((resolve, reject) => {
    wx.request(Object.assign(withAuth({
      url: `${BASE_URL}/${order_id}`,
      method: 'GET',
      success: (res) => {
        // 始终返回完整响应对象，便于全局统一判断
        resolve(res.data);
      },
      fail: reject
    })));
  });
}

function createOrder(data) {
  return new Promise((resolve, reject) => {
    wx.request(Object.assign(withAuth({
      url: BASE_URL,
      method: 'POST',
      data,
      success: (res) => {
        resolve(res.data);
      },
      fail: reject
    })));
  });
}

function updateOrder(order_id, data) {
  return new Promise((resolve, reject) => {
    wx.request(Object.assign(withAuth({
      url: `${BASE_URL}/${order_id}`,
      method: 'PUT',
      data,
      success: (res) => {
        resolve(res.data);
      },
      fail: reject
    })));
  });
}

function deleteOrder(order_id) {
  return new Promise((resolve, reject) => {
    wx.request(Object.assign(withAuth({
      url: `${BASE_URL}/${order_id}`,
      method: 'DELETE',
      success: (res) => {
        resolve(res.data);
      },
      fail: reject
    })));
  });
}

function payOrder(order_id) {
  return new Promise((resolve, reject) => {
    wx.request(Object.assign(withAuth({
      url: `${BASE_URL}/${order_id}/pay`,
      method: 'POST',
      success: (res) => {
        resolve(res.data);
      },
      fail: reject
    })));
  });
}

function confirmOrder(orderId) {
  return new Promise((resolve, reject) => {
    wx.request({
      url: `${API_BASE}/orders/service_orders/${orderId}/start`,
      method: 'POST',
      header: {
        'Authorization': 'Bearer ' + wx.getStorageSync('token'),
        'Content-Type': 'application/json'
      },
      success: (res) => {
        resolve(res.data);
      },
      fail: reject
    });
  });
}

function assignOrder(orderId, data) {
  return new Promise((resolve, reject) => {
    wx.request({
      url: `${API_BASE}/orders/service_orders/${orderId}/assign`,
      method: 'POST',
      data: data || {},
      header: {
        'Authorization': 'Bearer ' + wx.getStorageSync('token'),
        'Content-Type': 'application/json'
      },
      success: (res) => {
        resolve(res.data);
      },
      fail: reject
    });
  });
}

function completeOrder(orderId) {
  return new Promise((resolve, reject) => {
    wx.request({
      url: `${API_BASE}/orders/service_orders/${orderId}/complete`,
      method: 'POST',
      header: {
        'Authorization': 'Bearer ' + wx.getStorageSync('token'),
        'Content-Type': 'application/json'
      },
      success: (res) => {
        resolve(res.data);
      },
      fail: reject
    });
  });
}

// 药品订单状态流转接口
function deliverOrder(medicineOrderId) {
  return new Promise((resolve, reject) => {
    wx.request(Object.assign(withAuth({
      url: `${API_BASE}/orders/medicine_orders/${medicineOrderId}/status`,
      method: 'POST',
      data: { status: 'shipped' },
      header: { 'Content-Type': 'application/json' },
      success: (res) => {
        resolve(res.data);
      },
      fail: reject
    })));
  });
}

function completeMedicineOrder(medicineOrderId) {
  return new Promise((resolve, reject) => {
    wx.request(Object.assign(withAuth({
      url: `${API_BASE}/orders/medicine_orders/${medicineOrderId}/status`,
      method: 'POST',
      data: { status: 'delivered' },
      header: { 'Content-Type': 'application/json' },
      success: (res) => {
        resolve(res.data);
      },
      fail: reject
    })));
  });
}

function prepareOrder(medicineOrderId) {
  return new Promise((resolve, reject) => {
    wx.request(Object.assign(withAuth({
      url: `${API_BASE}/orders/medicine_orders/${medicineOrderId}/status`,
      method: 'POST',
      data: { status: 'preparing' },
      header: { 'Content-Type': 'application/json' },
      success: (res) => resolve(res.data),
      fail: reject
    })));
  });
}

function confirmReceiveMedicineOrder(medicineOrderId) {
  return new Promise((resolve, reject) => {
    wx.request(Object.assign(withAuth({
      url: `${API_BASE}/orders/medicine_orders/${medicineOrderId}/status`,
      method: 'POST',
      data: { status: 'delivered' },
      header: { 'Content-Type': 'application/json' },
      success: (res) => resolve(res.data),
      fail: reject
    })));
  });
}

export { getOrders, getOrderDetail, createOrder, updateOrder, deleteOrder, payOrder, confirmOrder, assignOrder, completeOrder, deliverOrder, completeMedicineOrder, prepareOrder, confirmReceiveMedicineOrder };

export function getServiceOrdersByUser(userId) {
  const token = wx.getStorageSync('token');
  const userInfo = wx.getStorageSync('userInfo');
  
  // 验证用户权限
  if (!userInfo) {
    return Promise.reject(new Error('用户信息不存在'));
  }
  
  const currentUserId = userInfo.user_id || userInfo.id;
  
  // 确保只能查询当前用户的订单
  if (userId != currentUserId) {
    console.warn('权限验证失败：尝试查询其他用户的订单');
    return Promise.reject(new Error('权限不足'));
  }
  
  return new Promise((resolve, reject) => {
    // 修复参数名：userId -> user_id
    wx.request({
      url: `${API_BASE}/orders/service_orders?user_id=${userId}`,
      method: 'GET',
      header: {
        'content-type': 'application/json',
        'Authorization': 'Bearer ' + token
      },
      success: res => {
        console.log('[getServiceOrdersByUser] API响应:', res);
        
        // 兼容后端返回结构，直接返回 orders 和 pagination
        if (res.data && res.data.data && res.data.data.orders) {
          // 前端再次过滤确保安全
          const orders = res.data.data.orders.filter(order => {
            return order.userId == userId || order.user_id == userId;
          });
          
          console.log('[getServiceOrdersByUser] 过滤后订单数量:', orders.length);
          
          resolve({
            orders: orders,
            pagination: res.data.data.pagination
          });
        } else {
          resolve(res.data);
        }
      },
      fail: reject
    });
  });
} 