// 套餐相关 API
const { API_BASE } = require('./base.js');
const BASE_URL = `${API_BASE}/packages`;
const auth = require('../utils/auth.js');

/**
 * 获取套餐列表
 * @param {Object} params - 查询参数
 * @param {string} params.type - 套餐类型：accompaniment/agency/hospitalization
 * @param {number} params.page - 页码
 * @param {number} params.limit - 每页数量
 * @returns {Promise}
 */
function getPackages(params) {
  return new Promise((resolve, reject) => {
    auth.requestWithToken({
      url: BASE_URL,
      method: 'GET',
      data: params,
      success: (res) => {
        console.log('获取套餐列表成功:', res.data);
        if (res.data && res.data.success) {
          // 返回套餐数组和分页信息
          resolve({
            packages: res.data.data.packages || [],
            pagination: res.data.data.pagination || {}
          });
        } else {
          reject(res.data && res.data.message ? res.data.message : '获取套餐列表失败');
        }
      },
      fail: (err) => {
        console.error('获取套餐列表失败:', err);
        reject(err);
      }
    });
  });
}

/**
 * 获取套餐详情
 * @param {number|string} packageId - 套餐ID
 * @returns {Promise}
 */
function getPackageDetail(packageId) {
  return new Promise((resolve, reject) => {
    if (!packageId) {
      reject(new Error('套餐ID不能为空'));
      return;
    }

    auth.requestWithToken({
      url: `${BASE_URL}/${packageId}`,
      method: 'GET',
      success: (res) => {
        console.log('获取套餐详情成功:', res.data);
        if (res.data && res.data.success) {
          resolve(res.data.data);
        } else {
          reject(res.data && res.data.message ? res.data.message : '获取套餐详情失败');
        }
      },
      fail: (err) => {
        console.error('获取套餐详情失败:', err);
        reject(err);
      }
    });
  });
}

/**
 * 根据套餐类型获取对应的预约页面路径
 * @param {string} type - 套餐类型
 * @returns {string} 预约页面路径
 */
function getBookingPageByType(type) {
  const typePageMap = {
    'accompaniment': '/pages/accompany/accompany',
    'agency': '/pages/accompany/accompany', // 代办问诊也使用陪诊页面
    'hospitalization': '/pages/hospitalize/hospitalize'
  };
  
  return typePageMap[type] || '/pages/accompany/accompany';
}

/**
 * 根据套餐类型获取服务类型标识
 * @param {string} type - 套餐类型
 * @returns {string} 服务类型
 */
function getServiceTypeByPackageType(type) {
  const typeServiceMap = {
    'accompaniment': 'companion',
    'agency': 'companion', // 代办问诊也使用companion服务类型
    'hospitalization': 'hospitalization'
  };
  
  return typeServiceMap[type] || 'companion';
}

module.exports = {
  getPackages,
  getPackageDetail,
  getBookingPageByType,
  getServiceTypeByPackageType
};
