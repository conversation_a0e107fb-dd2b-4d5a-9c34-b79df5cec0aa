// 药品订单表 API
const { API_BASE } = require('./base.js');
const BASE_URL = `${API_BASE}/orders/medicine_orders`;
const { withAuth } = require('../utils/auth.js');

export function getMedicineOrders(params) {
  return new Promise((resolve, reject) => {
    // 获取当前用户信息，确保权限验证
    const userInfo = wx.getStorageSync('userInfo') || {};
    const userType = userInfo.user_type;
    
    // 确保病患用户只能查询自己的订单
    if (userType === 'patient' && !params.userId) {
      params.userId = userInfo.id;
    }
    
    // 确保医护人员只能查询分配给自己的订单
    if ((userType === 'staff' || userType === 'medical_staff') && !params.staffId) {
      params.staffId = userInfo.staffId;
    }
    
    wx.request(Object.assign(withAuth({
      url: BASE_URL,
      method: 'GET',
      data: params,
      success: resolve,
      fail: reject
    })));
  });
}

export function getMedicineOrderDetail(medicine_order_id) {
  return new Promise((resolve, reject) => {
    wx.request(Object.assign(withAuth({
      url: `${BASE_URL}/${medicine_order_id}`,
      method: 'GET',
      success: resolve,
      fail: reject
    })));
  });
}

export function createMedicineOrder(data) {
  return new Promise((resolve, reject) => {
    wx.request({
      url: BASE_URL,
      method: 'POST',
      data,
      header: {
        'Authorization': 'Bearer ' + wx.getStorageSync('token'),
        'Content-Type': 'application/json'
      },
      success: (res) => resolve(res.data),
      fail: reject
    });
  });
}

export function updateMedicineOrder(medicine_order_id, data) {
  return new Promise((resolve, reject) => {
    wx.request(Object.assign(withAuth({
      url: `${BASE_URL}/${medicine_order_id}`,
      method: 'PUT',
      data,
      success: resolve,
      fail: reject
    })));
  });
}

export function deleteMedicineOrder(medicine_order_id) {
  return new Promise((resolve, reject) => {
    wx.request(Object.assign(withAuth({
      url: `${BASE_URL}/${medicine_order_id}`,
      method: 'DELETE',
      success: resolve,
      fail: reject
    })));
  });
}

export function createMedicineOrders(data) {
  return new Promise((resolve, reject) => {
    wx.request(Object.assign(withAuth({
      url: BASE_URL,
      method: 'POST',
      data,
      success: (res) => resolve(res.data),
      fail: reject
    })));
  });
} 