// 消息表 API
import { API_BASE } from './base.js';
const { withAuth } = require('../utils/auth.js');

/**
 * 获取消息列表
 * @param {Object} params - 查询参数，如 { page, pageSize, receiverId, keyword }
 * @returns {Promise} 响应结构为：
 *   {
 *     messages: [ { id, senderId, receiverId, orderId, content, ... } ],
 *     pagination: { total, page, limit, totalPages }
 *   }
 * 用途：用于消息中心、通知等场景，前端应通过 res.messages 获取消息数组。
 */
function getMessages(params) {
  return new Promise((resolve, reject) => {
    // 获取当前用户信息，确保权限验证
    const userInfo = wx.getStorageSync('userInfo') || {};
    const userType = userInfo.user_type;
    
    // 确保用户只能查询自己的消息
    if (userType === 'patient' && !params.receiverId && !params.senderId) {
      params.receiverId = userInfo.id;
    }
    
    // 确保医护人员只能查询自己的消息
    if ((userType === 'staff' || userType === 'medical_staff') && !params.receiverId && !params.senderId) {
      params.receiverId = userInfo.id;
    }
    
    wx.request(Object.assign(withAuth({
      url: `${API_BASE}/social/messages`,
      method: 'GET',
      data: params,
      header: {
        'Content-Type': 'application/json'
      },
      success: (res) => {
        if (res.data && res.data.data && res.data.data.messages) {
          resolve({
            messages: res.data.data.messages,
            pagination: res.data.data.pagination
          });
        } else {
          resolve(res.data);
        }
      },
      fail: reject
    })));
  });
}

const BASE_URL = `${API_BASE}/social/messages`;

function updateMessage(message_id, data) {
  return new Promise((resolve, reject) => {
    wx.request(Object.assign(withAuth({
      url: `${BASE_URL}/${message_id}`,
      method: 'PUT',
      data,
      header: {
        'Content-Type': 'application/json'
      },
      success: resolve,
      fail: reject
    })));
  });
}

function getMessageDetail(message_id) {
  return new Promise((resolve, reject) => {
    wx.request(Object.assign(withAuth({
      url: `${BASE_URL}/${message_id}`,
      method: 'GET',
      header: {
        'Content-Type': 'application/json'
      },
      success: resolve,
      fail: reject
    })));
  });
}

function createMessage(data) {
  return new Promise((resolve, reject) => {
    wx.request(Object.assign(withAuth({
      url: BASE_URL,
      method: 'POST',
      data,
      header: {
        'Content-Type': 'application/json'
      },
      success: resolve,
      fail: reject
    })));
  });
}

function deleteMessage(message_id) {
  return new Promise((resolve, reject) => {
    wx.request(Object.assign(withAuth({
      url: `${BASE_URL}/${message_id}`,
      method: 'DELETE',
      header: {
        'Content-Type': 'application/json'
      },
      success: resolve,
      fail: reject
    })));
  });
}

/**
 * 单条消息标记为已读
 * @param {number|string} messageId
 * @returns {Promise<{success: boolean, data: object, message?: string, error?: string}>}
 */
function markMessageRead(messageId) {
  return new Promise((resolve, reject) => {
    wx.request(Object.assign(withAuth({
      url: `${API_BASE}/social/messages/${messageId}/read`,
      method: 'PUT',
      header: {
        'Content-Type': 'application/json'
      },
      success: (res) => resolve(res.data),
      fail: reject
    })));
  });
}

/**
 * 批量标记消息为已读
 * @param {Array<number|string>} messageIds
 * @param {number|string} [senderId]
 * @returns {Promise<{success: boolean, data: object, message?: string, error?: string}>}
 */
function markMessagesBatchRead(messageIds, senderId) {
  return new Promise((resolve, reject) => {
    wx.request(Object.assign(withAuth({
      url: `${API_BASE}/social/messages/batch/read`,
      method: 'PUT',
      data: senderId ? { messageIds, senderId } : { messageIds },
      header: {
        'Content-Type': 'application/json'
      },
      success: (res) => resolve(res.data),
      fail: reject
    })));
  });
}

module.exports = {
  getMessages,
  getMessageDetail,
  createMessage,
  updateMessage,
  deleteMessage,
  markMessageRead,
  markMessagesBatchRead
}; 