// 挂号信息表 API
const { API_BASE } = require('./base.js');
const BASE_URL = `${API_BASE}/orders/registrations`;

function withAuth(options = {}) {
  const token = wx.getStorageSync('token');
  if (!token) {
    return Promise.reject('未登录');
  }
  options.header = Object.assign({}, options.header, {
    'Authorization': 'Bearer ' + token
  });
  return options;
}

export function getRegistrations(params) {
  return new Promise((resolve, reject) => {
    // 获取当前用户信息，确保权限验证
    const userInfo = wx.getStorageSync('userInfo') || {};
    const userType = userInfo.user_type;
    
    // 确保病患用户只能查询自己的订单
    if (userType === 'patient' && !params.userId) {
      params.userId = userInfo.id;
    }
    
    // 确保医护人员只能查询分配给自己的订单
    if ((userType === 'staff' || userType === 'medical_staff') && !params.staffId) {
      params.staffId = userInfo.staffId;
    }
    
    wx.request(Object.assign(withAuth({
      url: BASE_URL,
      method: 'GET',
      data: params,
      success: (res) => {
        resolve(res.data);
      },
      fail: reject
    })));
  });
}

export function getRegistrationList(params) {
  // 支持分页和按患者姓名筛选
  return new Promise((resolve, reject) => {
    wx.request(Object.assign(withAuth({
      url: BASE_URL,
      method: 'GET',
      data: params,
      success: (res) => {
        resolve(res.data);
      },
      fail: reject
    })));
  });
}

export function getRegistrationDetail(registration_id) {
  return new Promise((resolve, reject) => {
    wx.request(Object.assign(withAuth({
      url: `${BASE_URL}/${registration_id}`,
      method: 'GET',
      success: (res) => {
        resolve(res.data);
      },
      fail: reject
    })));
  });
}

export function createRegistration(data) {
  return new Promise((resolve, reject) => {
    wx.request(Object.assign(withAuth({
      url: BASE_URL,
      method: 'POST',
      data,
      success: (res) => {
        resolve(res.data);
      },
      fail: reject
    })));
  });
}

export function updateRegistration(registration_id, data) {
  return new Promise((resolve, reject) => {
    wx.request(Object.assign(withAuth({
      url: `${BASE_URL}/${registration_id}`,
      method: 'PUT',
      data,
      success: (res) => {
        resolve(res.data);
      },
      fail: reject
    })));
  });
}

export function deleteRegistration(registration_id) {
  return new Promise((resolve, reject) => {
    wx.request(Object.assign(withAuth({
      url: `${BASE_URL}/${registration_id}`,
      method: 'DELETE',
      success: (res) => {
        resolve(res.data);
      },
      fail: reject
    })));
  });
} 