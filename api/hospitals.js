// 医院表 API
const { API_BASE } = require('./base.js');
const BASE_URL = `${API_BASE}/medical/hospitals`;

/**
 * 获取医院列表
 * @param {Object} params - 查询参数
 * @returns {Promise} 响应结构为：
 *   {
 *     success: true,
 *     data: {
 *       hospitals: [ { id, name, ... }, ... ],
 *       pagination: {...}
 *     }
 *   }
 * 前端应通过 res.hospitals 获取医院数组
 */
function getHospitals(params) {
  return new Promise((resolve, reject) => {
    wx.request({
      url: BASE_URL,
      method: 'GET',
      data: params,
      header: {
        'Authorization': 'Bearer ' + wx.getStorageSync('token'),
        'Content-Type': 'application/json'
      },
      success: (res) => {
        // 兼容后端返回结构，直接返回 hospitals 和 pagination
        if (res.data && res.data.data && res.data.data.hospitals) {
          resolve({
            hospitals: res.data.data.hospitals,
            pagination: res.data.data.pagination
          });
        } else {
          resolve(res.data);
        }
      },
      fail: reject
    });
  });
}

function getHospitalDetail(id) {
  return new Promise((resolve, reject) => {
    wx.request({
      url: `${BASE_URL}/${id}`,
      method: 'GET',
      header: {
        'Authorization': 'Bearer ' + wx.getStorageSync('token'),
        'Content-Type': 'application/json'
      },
      success: (res) => {
        resolve(res.data);
      },
      fail: reject
    });
  });
}

module.exports = { getHospitals, getHospitalDetail }; 