// 用户表 API
const { API_BASE } = require('./base.js');
const BASE_URL = `${API_BASE}/users`;
const { withAuth } = require('../utils/auth.js');

// 注册用户
function createUser(data) {
  return new Promise((resolve, reject) => {
    wx.request({
      url: BASE_URL,
      method: 'POST',
      data,
      header: {
        'Content-Type': 'application/json'
      },
      success: resolve,
      fail: reject
    });
  });
}

// 登录用户
function loginUser(data) {
  return new Promise((resolve, reject) => {
    wx.request({
      url: `${API_BASE}/users/login`,
      method: 'POST',
      data,
      header: {
        'Content-Type': 'application/json'
      },
      success: resolve,
      fail: reject
    });
  });
}

// 获取用户列表
function getUsers(params) {
  return new Promise((resolve, reject) => {
    wx.request(Object.assign(withAuth({
      url: BASE_URL,
      method: 'GET',
      data: params,
      success: resolve,
      fail: reject
    })));
  });
}

// 获取用户详情
function getUserDetail(user_id) {
  return new Promise((resolve, reject) => {
    wx.request(Object.assign(withAuth({
      url: `${BASE_URL}/${user_id}`,
      method: 'GET',
      success: resolve,
      fail: reject
    })));
  });
}

// 更新用户信息
function updateUser(user_id, data) {
  return new Promise((resolve, reject) => {
    wx.request(Object.assign(withAuth({
      url: `${BASE_URL}/${user_id}`,
      method: 'PUT',
      data,
      success: resolve,
      fail: reject
    })));
  });
}

// 删除用户
function deleteUser(user_id) {
  return new Promise((resolve, reject) => {
    wx.request(Object.assign(withAuth({
      url: `${BASE_URL}/${user_id}`,
      method: 'DELETE',
      success: resolve,
      fail: reject
    })));
  });
}

module.exports = {
  createUser,
  loginUser,
  getUsers,
  getUserDetail,
  updateUser,
  deleteUser
}; 