const auth = require('../utils/auth.js');
const { API_BASE } = require('./base.js');

function getPatientList(options = {}) {
  return new Promise((resolve, reject) => {
    // 获取当前用户信息，确保权限验证
    const userInfo = wx.getStorageSync('userInfo') || {};
    const userType = userInfo.user_type;
    
    console.log('getPatientList 用户信息:', {
      userType: userType,
      userId: userInfo.id,
      options: options
    });
    
    // 确保用户只能查询自己的病患资料
    if (userType === 'patient' && !options.userId) {
      options.userId = userInfo.id;
    }
    
    // 医护人员可以查看所有病患资料（用于服务）
    if (userType === 'staff' || userType === 'medical_staff') {
      // 医护人员可以查看所有病患，但建议添加适当的权限控制
      // 这里可以根据具体业务需求进行调整
    }
    
    console.log('发送请求参数:', options);
    
    auth.requestWithToken({
      url: `${API_BASE}/system/patients`,
      method: 'GET',
      data: options,
      success: (res) => {
        console.log('API响应:', res.data);
        if (res.data && res.data.success) {
          resolve(res.data.data || []);
        } else {
          reject(res.data && res.data.message ? res.data.message : '获取病人列表失败');
        }
      },
      fail: (err) => {
        console.error('API请求失败:', err);
        reject(err);
      }
    });
  });
}

function addPatient(data) {
  return new Promise((resolve, reject) => {
    auth.requestWithToken({
      url: `${API_BASE}/system/patients`,
      method: 'POST',
      data,
      success: (res) => res.data && res.data.success ? resolve(res.data.data) : reject(res.data.message || '添加失败'),
      fail: reject
    });
  });
}

function updatePatient(id, data) {
  return new Promise((resolve, reject) => {
    auth.requestWithToken({
      url: `${API_BASE}/system/patients/${id}`,
      method: 'PUT',
      data,
      success: (res) => res.data && res.data.success ? resolve(res.data.data) : reject(res.data.message || '修改失败'),
      fail: reject
    });
  });
}

function deletePatient(id) {
  return new Promise((resolve, reject) => {
    auth.requestWithToken({
      url: `${API_BASE}/system/patients/${id}`,
      method: 'DELETE',
      success: (res) => res.data && res.data.success ? resolve() : reject(res.data.message || '删除失败'),
      fail: reject
    });
  });
}

module.exports = {
  getPatientList,
  addPatient,
  updatePatient,
  deletePatient
}; 