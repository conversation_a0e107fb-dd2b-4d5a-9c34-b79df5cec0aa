const { API_BASE } = require('./base.js');
const BASE_URL = `${API_BASE}/social`;

function postReview(data) {
  return new Promise((resolve, reject) => {
    const token = wx.getStorageSync('token');
    
    // 打印发送的数据用于调试
    console.log('[API] postReview 发送数据:', data);
    
    wx.request({
      url: BASE_URL + '/reviews',
      method: 'POST',
      data,
      header: {
        'content-type': 'application/json',
        'Authorization': token ? 'Bearer ' + token : ''
      },
      success: res => {
        console.log('[API] postReview 响应:', res);
        resolve(res.data);
      },
      fail: err => {
        console.error('[API] postReview 错误:', err);
        reject(err);
      }
    });
  });
}

function getReviewsByOrderId(orderId) {
  const token = wx.getStorageSync('token');
  return new Promise((resolve, reject) => {
    // 获取当前用户信息，确保权限验证
    const userInfo = wx.getStorageSync('userInfo') || {};
    const userType = userInfo.user_type;
    
    // 验证用户是否有权限查看该订单的评价
    // 这里需要根据具体业务逻辑进行验证
    // 例如：用户只能查看自己相关订单的评价
    
    wx.request({
      url: BASE_URL + '/reviews?orderId=' + orderId,
      method: 'GET',
      header: {
        'content-type': 'application/json',
        'Authorization': token ? 'Bearer ' + token : ''
      },
      success: res => {
        console.log('[API] getReviewsByOrderId 响应:', res);
        
        // 处理不同的响应数据结构
        let reviews = [];
        if (res.data && res.data.success) {
          if (Array.isArray(res.data.data)) {
            // 直接返回数组
            reviews = res.data.data;
          } else if (res.data.data && Array.isArray(res.data.data.reviews)) {
            // 嵌套在reviews字段中
            reviews = res.data.data.reviews;
          } else if (Array.isArray(res.data.reviews)) {
            // 直接在data.reviews中
            reviews = res.data.reviews;
          }
        } else if (Array.isArray(res.data)) {
          // 直接返回数组
          reviews = res.data;
        }
        
        console.log('[API] getReviewsByOrderId 解析后的评价数据:', reviews);
        resolve(reviews);
      },
      fail: err => {
        console.error('[API] getReviewsByOrderId 错误:', err);
        reject(err);
      }
    });
  });
}

function getReviewsByStaffId(staffId) {
  const token = wx.getStorageSync('token');
  return new Promise((resolve, reject) => {
    // 获取当前用户信息，确保权限验证
    const userInfo = wx.getStorageSync('userInfo') || {};
    const userType = userInfo.user_type;
    
    // 验证用户是否有权限查看该医护人员的评价
    if (userType === 'staff' || userType === 'medical_staff') {
      // 医护人员只能查看自己的评价
      if (String(userInfo.staffId) !== String(staffId)) {
        reject(new Error('无权查看该医护人员的评价'));
        return;
      }
    }
    
    wx.request({
      url: BASE_URL + '/reviews?staffId=' + staffId,
      method: 'GET',
      header: {
        'content-type': 'application/json',
        'Authorization': token ? 'Bearer ' + token : ''
      },
      success: res => {
        console.log('[API] getReviewsByStaffId 响应:', res);
        
        // 处理不同的响应数据结构
        let reviews = [];
        if (res.data && res.data.success) {
          if (Array.isArray(res.data.data)) {
            // 直接返回数组
            reviews = res.data.data;
          } else if (res.data.data && Array.isArray(res.data.data.reviews)) {
            // 嵌套在reviews字段中
            reviews = res.data.data.reviews;
          } else if (Array.isArray(res.data.reviews)) {
            // 直接在data.reviews中
            reviews = res.data.reviews;
          }
        } else if (Array.isArray(res.data)) {
          // 直接返回数组
          reviews = res.data;
        }
        
        console.log('[API] getReviewsByStaffId 解析后的评价数据:', reviews);
        resolve(reviews);
      },
      fail: err => {
        console.error('[API] getReviewsByStaffId 错误:', err);
        reject(err);
      }
    });
  });
}

module.exports = {
  postReview,
  getReviewsByOrderId,
  getReviewsByStaffId
}; 