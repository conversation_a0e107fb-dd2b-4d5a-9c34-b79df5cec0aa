// 医护人员表 API
const { API_BASE } = require('./base.js');
const BASE_URL = `${API_BASE}/medical/medical_staff`;

function getMedicalStaff(params) {
  return new Promise((resolve, reject) => {
    wx.request({
      url: BASE_URL,
      method: 'GET',
      data: params,
      header: {
        'Authorization': 'Bearer ' + wx.getStorageSync('token'),
        'Content-Type': 'application/json'
      },
      success: resolve,
      fail: reject
    });
  });
}

module.exports = { getMedicalStaff }; 