// 评价表 API
const BASE_URL = '/api/reviews';
const { withAuth } = require('../utils/auth.js');

export function getReviews(params) {
  return new Promise((resolve, reject) => {
    // 获取当前用户信息，确保权限验证
    const userInfo = wx.getStorageSync('userInfo') || {};
    const userType = userInfo.user_type;
    
    // 确保用户只能查询相关的评价
    if (userType === 'patient' && !params.reviewerId && !params.orderId) {
      // 病患用户只能查看自己的评价或相关订单的评价
      params.reviewerId = userInfo.id;
    }
    
    // 医护人员可以查看自己的评价或相关订单的评价
    if ((userType === 'staff' || userType === 'medical_staff') && !params.staffId && !params.orderId) {
      params.staffId = userInfo.staffId;
    }
    
    wx.request(Object.assign(withAuth({
      url: BASE_URL,
      method: 'GET',
      data: params,
      success: resolve,
      fail: reject
    })));
  });
}

export function getReviewDetail(review_id) {
  return new Promise((resolve, reject) => {
    wx.request(Object.assign(withAuth({
      url: `${BASE_URL}/${review_id}`,
      method: 'GET',
      success: resolve,
      fail: reject
    })));
  });
}

export function createReview(data) {
  return new Promise((resolve, reject) => {
    wx.request(Object.assign(withAuth({
      url: BASE_URL,
      method: 'POST',
      data,
      success: resolve,
      fail: reject
    })));
  });
}

export function updateReview(review_id, data) {
  return new Promise((resolve, reject) => {
    wx.request(Object.assign(withAuth({
      url: `${BASE_URL}/${review_id}`,
      method: 'PUT',
      data,
      success: resolve,
      fail: reject
    })));
  });
}

export function deleteReview(review_id) {
  return new Promise((resolve, reject) => {
    wx.request(Object.assign(withAuth({
      url: `${BASE_URL}/${review_id}`,
      method: 'DELETE',
      success: resolve,
      fail: reject
    })));
  });
} 