const navbarBehavior = Behavior({
  properties: {
    // 页面标题
    pageTitle: {
      type: String,
      value: ''
    },
    // 是否显示返回按钮
    showBackButton: {
      type: Boolean,
      value: true
    },
    // 导航栏文字颜色
    navbarTextColor: {
      type: String,
      value: '#000000'
    },
    // 导航栏背景颜色
    navbarBgColor: {
      type: String,
      value: '#ffffff'
    },
    // 自定义导航栏高度
    customNavbarHeight: {
      type: Number,
      value: 0
    },
    // 内容区域顶部间距
    contentTopMargin: {
      type: Number,
      value: 0
    }
  },

  data: {
    navbarHeight: 0,
    statusBarHeight: 0,
    contentTop: 0,
    navbarInfo: {}
  },

  lifetimes: {
    attached() {
      this.initNavbarData();
    }
  },

  methods: {
    // 初始化导航栏数据
    initNavbarData() {
      const systemInfo = wx.getSystemInfoSync();
      const menuButtonInfo = wx.getMenuButtonBoundingClientRect();
      
      const statusBarHeight = systemInfo.statusBarHeight;
      const contentTop = statusBarHeight + (menuButtonInfo.top - statusBarHeight);
      
      // 如果设置了自定义高度，使用自定义高度
      let navbarHeight;
      if (this.properties.customNavbarHeight > 0) {
        navbarHeight = this.properties.customNavbarHeight;
      } else {
        // 自动计算导航栏总高度
        navbarHeight = statusBarHeight + (menuButtonInfo.top - statusBarHeight) * 2 + menuButtonInfo.height;
      }
      
      const navbarInfo = {
        navbarHeight,
        contentTop,
        statusBarHeight,
        menuButtonInfo
      };
      
      this.setData({
        statusBarHeight,
        navbarHeight,
        contentTop,
        navbarInfo
      });
    },

    // 处理导航栏准备完成事件
    onNavbarReady(e) {
      const { navbarHeight, contentTop, statusBarHeight } = e.detail;
      this.setData({
        navbarHeight,
        contentTop,
        statusBarHeight
      });
    },

    // 设置精确的内容顶部间距
    setContentTopMargin(margin) {
      this.setData({
        contentTopMargin: margin
      });
    },

    // 获取精确的内容顶部位置
    getContentTopPosition() {
      const { navbarHeight, contentTopMargin } = this.data;
      return navbarHeight + contentTopMargin;
    },

    // 返回上一页
    goBack() {
      const pages = getCurrentPages();
      if (pages.length > 1) {
        wx.navigateBack({
          delta: 1
        });
      } else {
        // 如果是首页，跳转到首页
        wx.reLaunch({
          url: '/pages/home/<USER>'
        });
      }
    },

    // 返回首页
    goHome() {
      wx.reLaunch({
        url: '/pages/home/<USER>'
      });
    },

    // 设置页面标题
    setPageTitle(title) {
      this.setData({
        pageTitle: title
      });
    },

    // 获取导航栏信息
    getNavbarInfo() {
      return this.data.navbarInfo;
    }
  }
});

export default navbarBehavior; 