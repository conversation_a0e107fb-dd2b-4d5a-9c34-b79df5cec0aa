export const getObserver=(e,t)=>new Promise((o=>{e.createIntersectionObserver(e).relativeToViewport().observe(t,(e=>{o(e)}))}));export const getWindowInfo=()=>wx.getWindowInfo&&wx.getWindowInfo()||wx.getSystemInfoSync();export const getAppBaseInfo=()=>wx.getAppBaseInfo&&wx.getAppBaseInfo()||wx.getSystemInfoSync();export const getDeviceInfo=()=>wx.getDeviceInfo&&wx.getDeviceInfo()||wx.getSystemInfoSync();