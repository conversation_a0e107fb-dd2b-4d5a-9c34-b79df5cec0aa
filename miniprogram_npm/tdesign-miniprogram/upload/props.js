const props={addBtn:{type:Boolean,value:!0},addContent:{type:String},allowUploadDuplicateFile:{type:Boolean,value:!1},config:{type:Object},disabled:{type:null,value:void 0},draggable:{type:null},files:{type:Array,value:null},defaultFiles:{type:Array},gridConfig:{type:Object},gutter:{type:Number,value:16},imageProps:{type:Object},max:{type:Number,value:0},mediaType:{type:Array,value:["image","video"]},removeBtn:{type:Boolean,value:!0},requestMethod:{type:null},sizeLimit:{type:null},source:{type:String,value:"media"},transition:{type:Object,value:{backTransition:!0,duration:300,timingFunction:"ease"}}};export default props;