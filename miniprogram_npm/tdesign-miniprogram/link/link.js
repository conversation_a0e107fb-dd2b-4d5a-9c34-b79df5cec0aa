import{__decorate}from"tslib";import{SuperComponent,wxComponent}from"../common/src/index";import config from"../common/config";import props from"./props";import{calcIcon}from"../common/utils";const{prefix:prefix}=config,name=`${prefix}-link`;let Link=class extends SuperComponent{constructor(){super(...arguments),this.externalClasses=[`${prefix}-class`,`${prefix}-class-hover`,`${prefix}-class-prefix-icon`,`${prefix}-class-content`,`${prefix}-class-suffix-icon`],this.properties=props,this.options={multipleSlots:!0},this.data={prefix:prefix,classPrefix:name},this.observers={"theme, disabled, size, underline, navigatorProps"(){this.setClass()},prefixIcon(e){this.setData({_prefixIcon:calcIcon(e)})},suffixIcon(e){this.setData({_suffixIcon:calcIcon(e)})}},this.lifetimes={attached(){this.setClass()}},this.methods={setClass(){const{theme:e,size:s,underline:i,navigatorProps:t,disabled:o}=this.properties,n=[name,`${name}--${e}`,`${name}--${s}`];i&&n.push(`${name}--underline`),(t&&!t.url&&!["navigateBack","exit"].includes(t.openType)||o)&&n.push(`${name}--disabled`),this.setData({className:n.join(" ")})},onSuccess(e){this.triggerEvent("success",e)},onFail(e){this.triggerEvent("fail",e)},onComplete(e){this.triggerEvent("complete",e)}}}};Link=__decorate([wxComponent()],Link);export default Link;