import{__awaiter,__decorate}from"tslib";import{SuperComponent,wxComponent}from"../common/src/index";import config from"../common/config";import props from"./props";import{styles,addUnit,getRect}from"../common/utils";const{prefix:prefix}=config,name=`${prefix}-icon`;let Icon=class extends SuperComponent{constructor(){super(...arguments),this.externalClasses=[`${prefix}-class`],this.properties=props,this.data={componentPrefix:prefix,classPrefix:name,isImage:!1,iconStyle:void 0},this.observers={"name, color, size, style"(){this.setIconStyle()}},this.methods={onTap(t){this.triggerEvent("click",t.detail)},setIconStyle(){const{name:t,color:e,size:o,classPrefix:i}=this.data,s=-1!==t.indexOf("/"),n=addUnit(o),r=e?{color:e}:{},c=o?{"font-size":n}:{},a=Object.assign(Object.assign({},r),c);this.setData({isImage:s},(()=>__awaiter(this,void 0,void 0,(function*(){if(s){let t=n;t||(yield getRect(this,`.${i}`).then((e=>{t=addUnit(null==e?void 0:e.height)})).catch((()=>{}))),a.width=t,a.height=t}this.setData({iconStyle:`${styles(a)}`})}))))}}}};Icon=__decorate([wxComponent()],Icon);export default Icon;