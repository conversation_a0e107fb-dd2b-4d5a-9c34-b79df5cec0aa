import{__rest}from"tslib";import{MessageType}from"../message/message.interface";import{getInstance}from"../common/utils";const showMessage=function(e,s=MessageType.info){const{context:t,selector:o="#t-message"}=e,n=__rest(e,["context","selector"]),a=getInstance(t,o);if(a)return a.resetData((()=>{a.setData(Object.assign({theme:s},n),a.show.bind(a))})),a;console.error("未找到组件,请确认 selector && context 是否正确")};export default{info:e=>showMessage(e,MessageType.info),success:e=>showMessage(e,MessageType.success),warning:e=>showMessage(e,MessageType.warning),error:e=>showMessage(e,MessageType.error),hide(e){const{context:s,selector:t="#t-message"}=Object.assign({},e),o=getInstance(s,t);o&&o.hide()}};