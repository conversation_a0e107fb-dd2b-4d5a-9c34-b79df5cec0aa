# Assets 目录

此目录用于存放小程序所需的图片资源。

## 需要添加的图标文件

为了确保tabBar正常显示，请在此目录下添加以下图标文件：

### TabBar 图标
- `home.png` - 首页图标（未选中状态）
- `home-active.png` - 首页图标（选中状态）
- `service.png` - 服务图标（未选中状态）
- `service-active.png` - 服务图标（选中状态）
- `message.png` - 消息图标（未选中状态）
- `message-active.png` - 消息图标（选中状态）
- `mine.png` - 我的图标（未选中状态）
- `mine-active.png` - 我的图标（选中状态）

### 其他图标
- `default-avatar.png` - 默认头像
- `user-avatar.png` - 用户头像
- `doctor-avatar.png` - 医生头像
- `nurse-avatar.png` - 护士头像
- `doctor-avatar2.png` - 医生头像2
- `empty-message.png` - 空消息状态图
- `empty-notification.png` - 空通知状态图

## 图标规格建议
- TabBar图标：建议尺寸 81px × 81px，格式PNG
- 头像图标：建议尺寸 80px × 80px，格式PNG
- 状态图标：建议尺寸 200px × 200px，格式PNG

## 临时解决方案
如果暂时没有图标文件，可以：
1. 使用在线图标生成工具创建简单的图标
2. 从图标库下载免费图标
3. 暂时注释掉app.json中的iconPath配置，使用纯文字tabBar 