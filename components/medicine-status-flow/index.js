Component({
  properties: {
    // 主服务订单状态
    mainOrderStatus: {
      type: String,
      value: ''
    },
    // 药品子订单状态
    medicineOrderStatus: {
      type: String,
      value: ''
    },
    // 是否显示药品状态
    showMedicineStatus: {
      type: Boolean,
      value: true
    }
  },

  data: {
    // 主服务订单状态流转
    mainStatusFlow: [],
    // 药品子订单状态流转
    medicineStatusFlow: [],
    // 当前显示的状态文本
    currentStatusText: ''
  },

  lifetimes: {
    attached() {
      this.generateStatusFlow();
    }
  },

  observers: {
    'mainOrderStatus, medicineOrderStatus': function() {
      this.generateStatusFlow();
    }
  },

  methods: {
    // 生成状态流转数据
    generateStatusFlow() {
      const { mainOrderStatus, medicineOrderStatus, showMedicineStatus } = this.data;
      
      // 生成主服务订单状态流转
      const mainFlow = this.generateMainOrderFlow(mainOrderStatus);
      
      // 生成药品子订单状态流转
      const medicineFlow = showMedicineStatus ? this.generateMedicineOrderFlow(medicineOrderStatus) : [];
      
      // 生成当前状态显示文本
      const currentStatusText = this.generateCurrentStatusText(mainOrderStatus, medicineOrderStatus, showMedicineStatus);
      
      this.setData({
        mainStatusFlow: mainFlow,
        medicineStatusFlow: medicineFlow,
        currentStatusText: currentStatusText
      });
    },

    // 生成主服务订单状态流转
    generateMainOrderFlow(status) {
      const flow = [
        { status: 'pending', label: '待处理', completed: false, current: false, time: null },
        { status: 'paid', label: '已支付', completed: false, current: false, time: null },
        { status: 'assigned', label: '已分配', completed: false, current: false, time: null },
        { status: 'in_progress', label: '服务中', completed: false, current: false, time: null },
        { status: 'completed', label: '已完成', completed: false, current: false, time: null }
      ];
      
      // 根据当前状态更新流程
      flow.forEach((step, index) => {
        if (status === step.status) {
          step.current = true;
        } else if (this.isStatusCompleted(status, step.status)) {
          step.completed = true;
        }
      });
      
      return flow;
    },

    // 生成药品子订单状态流转
    generateMedicineOrderFlow(status) {
      const flow = [
        { status: 'pending', label: '待处理', completed: false, current: false, time: null },
        { status: 'preparing', label: '配药中', completed: false, current: false, time: null },
        { status: 'shipped', label: '已发货', completed: false, current: false, time: null },
        { status: 'delivered', label: '已送达', completed: false, current: false, time: null }
      ];
      
      // 根据当前状态更新流程
      flow.forEach((step, index) => {
        if (status === step.status) {
          step.current = true;
        } else if (this.isMedicineStatusCompleted(status, step.status)) {
          step.completed = true;
        }
      });
      
      return flow;
    },

    // 生成当前状态显示文本
    generateCurrentStatusText(mainStatus, medicineStatus, showMedicineStatus) {
      const mainStatusText = this.getMainStatusText(mainStatus);
      
      if (!showMedicineStatus || !medicineStatus) {
        return mainStatusText;
      }
      
      const medicineStatusText = this.getMedicineStatusText(medicineStatus);
      return `${mainStatusText} · ${medicineStatusText}`;
    },

    // 获取主服务订单状态文本
    getMainStatusText(status) {
      const statusMap = {
        'pending': '待处理',
        'paid': '已支付',
        'assigned': '已分配',
        'in_progress': '服务中',
        'completed': '已完成',
        'cancelled': '已取消'
      };
      return statusMap[status] || status;
    },

    // 获取药品订单状态文本
    getMedicineStatusText(status) {
      const statusMap = {
        'pending': '待处理',
        'preparing': '配药中',
        'shipped': '已发货',
        'delivered': '已送达',
        'completed': '已完成',
        'cancelled': '已取消'
      };
      return statusMap[status] || status;
    },

    // 判断主服务订单状态是否已完成
    isStatusCompleted(currentStatus, targetStatus) {
      const statusOrder = {
        'pending': 0,
        'paid': 1,
        'assigned': 2,
        'in_progress': 3,
        'completed': 4
      };
      
      const currentOrder = statusOrder[currentStatus] || 0;
      const targetOrder = statusOrder[targetStatus] || 0;
      
      return currentOrder > targetOrder;
    },

    // 判断药品订单状态是否已完成
    isMedicineStatusCompleted(currentStatus, targetStatus) {
      const statusOrder = {
        'pending': 0,
        'preparing': 1,
        'shipped': 2,
        'delivered': 3,
        'completed': 4
      };
      
      const currentOrder = statusOrder[currentStatus] || 0;
      const targetOrder = statusOrder[targetStatus] || 0;
      
      return currentOrder > targetOrder;
    },

    // 点击状态步骤
    onStatusStepTap(e) {
      const { status, type } = e.currentTarget.dataset;
      this.triggerEvent('statusStepTap', { status, type });
    }
  }
}); 