<!-- 药品订单状态流转组件 -->
<view class="medicine-status-flow">
  <!-- 当前状态显示 -->
  <view class="current-status">
    <view class="status-text">{{currentStatusText}}</view>
  </view>

  <!-- 主服务订单状态流转 -->
  <view class="status-section">
    <view class="section-title">
      <t-icon name="service" size="24rpx" color="#0C4147" />
      <text class="title-text">服务状态</text>
    </view>
    <view class="status-flow">
      <block wx:for="{{mainStatusFlow}}" wx:key="status">
        <view class="status-step {{item.completed ? 'completed' : ''}} {{item.current ? 'current' : ''}}"
              bindtap="onStatusStepTap" 
              data-status="{{item.status}}" 
              data-type="main">
          <view class="status-dot">
            <t-icon wx:if="{{item.completed}}" name="check" size="20rpx" color="#ffffff" />
          </view>
          <view class="status-step-content">
            <view class="status-step-label">{{item.label}}</view>
            <view class="status-step-time" wx:if="{{item.time}}">{{item.time}}</view>
          </view>
        </view>
        <view class="status-line" wx:if="{{index < mainStatusFlow.length - 1}}"></view>
      </block>
    </view>
  </view>

  <!-- 药品子订单状态流转（仅药品订单显示） -->
  <view class="status-section" wx:if="{{showMedicineStatus && medicineStatusFlow.length > 0}}">
    <view class="section-title">
      <t-icon name="medicine-box" size="24rpx" color="#0C4147" />
      <text class="title-text">配送状态</text>
    </view>
    <view class="status-flow">
      <block wx:for="{{medicineStatusFlow}}" wx:key="status">
        <view class="status-step {{item.completed ? 'completed' : ''}} {{item.current ? 'current' : ''}}"
              bindtap="onStatusStepTap" 
              data-status="{{item.status}}" 
              data-type="medicine">
          <view class="status-dot">
            <t-icon wx:if="{{item.completed}}" name="check" size="20rpx" color="#ffffff" />
          </view>
          <view class="status-step-content">
            <view class="status-step-label">{{item.label}}</view>
            <view class="status-step-time" wx:if="{{item.time}}">{{item.time}}</view>
          </view>
        </view>
        <view class="status-line" wx:if="{{index < medicineStatusFlow.length - 1}}"></view>
      </block>
    </view>
  </view>
</view> 