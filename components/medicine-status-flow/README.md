# 药品订单状态流转组件

## 概述

药品订单状态流转组件专门为药品订单（`serviceType: 'proxy'`）设计，提供主服务订单状态和药品子订单状态的双重显示，支持交互式状态查看。

## 功能特性

- **双重状态显示**：同时显示主服务订单状态和药品配送状态
- **交互式状态查看**：点击状态步骤可查看详细说明
- **状态流转可视化**：清晰的时间线展示订单进度
- **响应式设计**：适配不同屏幕尺寸

## 使用方法

### 1. 在页面中引入组件

```json
{
  "usingComponents": {
    "medicine-status-flow": "../../components/medicine-status-flow/index"
  }
}
```

### 2. 在WXML中使用

```xml
<!-- 基础用法 -->
<medicine-status-flow 
  mainOrderStatus="{{order.status}}"
  medicineOrderStatus="{{medicineOrderStatus}}"
  showMedicineStatus="{{true}}"
  bind:statusStepTap="onStatusStepTap"
/>
```

### 3. 在JS中处理事件

```javascript
Page({
  data: {
    order: {},
    medicineOrderStatus: ''
  },

  // 处理状态步骤点击
  onStatusStepTap(e) {
    const { status, type } = e.detail;
    console.log('状态步骤点击:', { status, type });
    
    // 显示状态说明
    this.showStatusInfo(status, type);
  },

  // 显示状态信息
  showStatusInfo(status, type) {
    const statusInfo = {
      'pending': '订单已创建，等待处理',
      'paid': '订单已支付，等待分配服务人员',
      'assigned': '已分配服务人员，等待开始服务',
      'in_progress': '服务正在进行中',
      'completed': '服务已完成',
      'preparing': '正在为您配药',
      'shipped': '药品已发货，正在配送中',
      'delivered': '药品已送达'
    };
    
    const info = statusInfo[status] || '当前状态';
    wx.showModal({
      title: `${type === 'main' ? '服务状态' : '配送状态'}说明`,
      content: info,
      showCancel: false,
      confirmText: '知道了'
    });
  }
});
```

## 属性说明

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| mainOrderStatus | String | '' | 主服务订单状态 |
| medicineOrderStatus | String | '' | 药品子订单状态 |
| showMedicineStatus | Boolean | true | 是否显示药品状态 |

## 事件说明

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| statusStepTap | 状态步骤点击事件 | { status, type } |

### 回调参数说明

- `status`: 状态值（pending/paid/assigned/in_progress/completed/preparing/shipped/delivered）
- `type`: 状态类型（main/medicine）

## 状态映射

### 主服务订单状态
- `pending` → 待处理
- `paid` → 已支付
- `assigned` → 已分配
- `in_progress` → 服务中
- `completed` → 已完成

### 药品子订单状态
- `pending` → 待处理
- `preparing` → 配药中
- `shipped` → 已发货
- `delivered` → 已送达

## 样式定制

组件使用CSS变量，可以通过以下方式定制样式：

```css
/* 自定义状态圆点颜色 */
.medicine-status-flow .status-dot {
  background-color: #your-color;
}

/* 自定义连接线颜色 */
.medicine-status-flow .status-line {
  background-color: #your-color;
}

/* 自定义文字颜色 */
.medicine-status-flow .status-step-label {
  color: #your-color;
}
```

## 注意事项

1. **数据同步**：确保主服务订单状态和药品子订单状态的数据同步
2. **状态顺序**：组件内部已处理状态流转顺序，无需手动排序
3. **交互反馈**：建议为状态步骤点击提供适当的用户反馈
4. **性能优化**：组件使用观察者模式，状态变化时自动更新

## 示例

完整的使用示例请参考 `pages/orders/detail.js` 和 `pages/orders/detail.wxml`。 