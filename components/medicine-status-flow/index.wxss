/* 药品订单状态流转组件样式 */
.medicine-status-flow {
  padding: 24rpx;
}

/* 当前状态显示 */
.current-status {
  margin-bottom: 32rpx;
  text-align: center;
}

.status-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #0C4147;
  background: linear-gradient(135deg, #0C4147 0%, #1a6b75 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 状态区块 */
.status-section {
  margin-bottom: 32rpx;
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
  padding-bottom: 12rpx;
  border-bottom: 1px solid #f0f0f0;
}

.title-text {
  margin-left: 8rpx;
  font-size: 28rpx;
  font-weight: 600;
  color: #0C4147;
}

/* 状态流转 */
.status-flow {
  position: relative;
}

.status-step {
  display: flex;
  align-items: flex-start;
  margin-bottom: 24rpx;
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
}

.status-step:hover {
  transform: translateX(8rpx);
}

.status-step.completed .status-dot {
  background-color: #34C759;
  border-color: #34C759;
}

.status-step.current .status-dot {
  background-color: #007AFF;
  border-color: #007AFF;
  box-shadow: 0 0 0 4rpx rgba(0, 122, 255, 0.2);
}

.status-step-content {
  margin-left: 16rpx;
  flex: 1;
}

.status-step-label {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 4rpx;
}

.status-step-time {
  font-size: 22rpx;
  color: #999;
}

/* 状态圆点 */
.status-dot {
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  background-color: #e0e0e0;
  border: 2rpx solid #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  transition: all 0.3s ease;
}

/* 连接线 */
.status-line {
  position: absolute;
  left: 15rpx;
  top: 32rpx;
  width: 2rpx;
  height: 24rpx;
  background-color: #e0e0e0;
  z-index: 1;
}

.status-step.completed + .status-line {
  background-color: #34C759;
}

/* 药品状态特殊样式 */
.status-section:last-child .status-step.current .status-dot {
  background-color: #FF9500;
  border-color: #FF9500;
  box-shadow: 0 0 0 4rpx rgba(255, 149, 0, 0.2);
}

.status-section:last-child .status-step.completed .status-dot {
  background-color: #34C759;
  border-color: #34C759;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .medicine-status-flow {
    padding: 16rpx;
  }
  
  .status-text {
    font-size: 28rpx;
  }
  
  .title-text {
    font-size: 24rpx;
  }
  
  .status-step-label {
    font-size: 24rpx;
  }
  
  .status-step-time {
    font-size: 20rpx;
  }
} 