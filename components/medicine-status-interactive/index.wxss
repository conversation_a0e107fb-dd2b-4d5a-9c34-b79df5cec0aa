/* 药品订单状态交互增强组件样式 */
.medicine-status-interactive {
  padding: 24rpx;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

/* 状态概览 */
.status-overview {
  margin-bottom: 32rpx;
  padding-bottom: 24rpx;
  border-bottom: 1px solid #f0f0f0;
}

.status-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.status-icon {
  margin-right: 16rpx;
  padding: 12rpx;
  background: linear-gradient(135deg, #0C4147 0%, #1a6b75 100%);
  border-radius: 12rpx;
}

.status-info {
  flex: 1;
}

.status-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #0C4147;
  margin-bottom: 8rpx;
}

.status-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

.estimated-time {
  display: flex;
  align-items: center;
  padding: 12rpx 16rpx;
  background: #fff7e6;
  border-radius: 8rpx;
  border-left: 4rpx solid #FF9500;
}

.time-text {
  margin-left: 8rpx;
  font-size: 24rpx;
  color: #FF9500;
  font-weight: 500;
}

/* 状态时间线 */
.status-timeline {
  margin-bottom: 32rpx;
}

.timeline-title {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
  padding-bottom: 12rpx;
  border-bottom: 1px solid #f0f0f0;
}

.title-text {
  margin-left: 8rpx;
  font-size: 28rpx;
  font-weight: 600;
  color: #0C4147;
}

.timeline-content {
  position: relative;
}

.timeline-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 32rpx;
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
}

.timeline-item:hover {
  transform: translateX(8rpx);
}

.timeline-item.completed .timeline-dot {
  box-shadow: 0 0 0 4rpx rgba(52, 199, 89, 0.2);
}

.timeline-item.current .timeline-dot {
  box-shadow: 0 0 0 6rpx rgba(0, 122, 255, 0.3);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { box-shadow: 0 0 0 6rpx rgba(0, 122, 255, 0.3); }
  50% { box-shadow: 0 0 0 8rpx rgba(0, 122, 255, 0.1); }
  100% { box-shadow: 0 0 0 6rpx rgba(0, 122, 255, 0.3); }
}

.timeline-dot {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  margin-right: 16rpx;
  transition: all 0.3s ease;
}

.timeline-content {
  flex: 1;
}

.timeline-label {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.label-text {
  margin-left: 8rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  flex: 1;
}

.status-badge {
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: 500;
}

.status-badge.main {
  background: #e3f2fd;
  color: #1976d2;
}

.status-badge.medicine {
  background: #fff3e0;
  color: #f57c00;
}

.timeline-desc {
  font-size: 24rpx;
  color: #666;
  margin-left: 32rpx;
  font-style: italic;
}

.timeline-line {
  position: absolute;
  left: 19rpx;
  top: 40rpx;
  width: 2rpx;
  height: 32rpx;
  background: linear-gradient(to bottom, #e0e0e0 0%, #f0f0f0 100%);
  z-index: 1;
}

.timeline-item.completed + .timeline-line {
  background: linear-gradient(to bottom, #34C759 0%, #4CAF50 100%);
}

/* 配送信息卡片 */
.delivery-card {
  margin-bottom: 24rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border-left: 4rpx solid #34C759;
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.card-title {
  margin-left: 8rpx;
  font-size: 26rpx;
  font-weight: 600;
  color: #0C4147;
}

.delivery-content {
  cursor: pointer;
}

.delivery-item {
  display: flex;
  margin-bottom: 8rpx;
}

.item-label {
  font-size: 24rpx;
  color: #666;
  min-width: 120rpx;
}

.item-value {
  font-size: 24rpx;
  color: #333;
  flex: 1;
}

/* 快捷操作 */
.quick-actions {
  display: flex;
  gap: 16rpx;
}

.action-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-item:hover {
  background: #e9ecef;
  transform: translateY(-2rpx);
}

.action-text {
  margin-top: 8rpx;
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .medicine-status-interactive {
    padding: 16rpx;
  }
  
  .status-title {
    font-size: 28rpx;
  }
  
  .status-desc {
    font-size: 24rpx;
  }
  
  .timeline-dot {
    width: 32rpx;
    height: 32rpx;
  }
  
  .label-text {
    font-size: 24rpx;
  }
  
  .quick-actions {
    flex-direction: column;
  }
} 