<!-- 药品订单状态交互增强组件 -->
<view class="medicine-status-interactive">
  <!-- 当前状态概览 -->
  <view class="status-overview">
    <view class="status-header">
      <view class="status-icon">
        <t-icon name="medicine-box" size="48rpx" color="#0C4147" />
      </view>
      <view class="status-info">
        <view class="status-title">药品订单状态</view>
        <view class="status-desc">{{currentStatusDesc}}</view>
      </view>
    </view>
    
    <!-- 预计完成时间 -->
    <view class="estimated-time" wx:if="{{estimatedTime}}">
      <t-icon name="time" size="24rpx" color="#FF9500" />
      <text class="time-text">预计完成时间：{{estimatedTime}}</text>
    </view>
  </view>

  <!-- 状态流转时间线 -->
  <view class="status-timeline">
    <view class="timeline-title">
      <t-icon name="list" size="24rpx" color="#0C4147" />
      <text class="title-text">订单进度</text>
    </view>
    
    <view class="timeline-content">
      <block wx:for="{{statusFlow}}" wx:key="index">
        <view class="timeline-item {{item.completed ? 'completed' : ''}} {{item.current ? 'current' : ''}}"
              bindtap="onStepTap"
              data-status="{{item.status}}"
              data-type="{{item.type}}"
              data-index="{{item.index}}">
          
          <!-- 时间线圆点 -->
          <view class="timeline-dot" style="background-color: {{item.color}}">
            <t-icon wx:if="{{item.completed}}" name="check" size="20rpx" color="#ffffff" />
            <t-icon wx:elif="{{item.current}}" name="{{item.icon}}" size="20rpx" color="#ffffff" />
          </view>
          
          <!-- 时间线内容 -->
          <view class="timeline-content">
            <view class="timeline-label">
              <t-icon name="{{item.icon}}" size="24rpx" color="{{item.color}}" />
              <text class="label-text">{{item.label}}</text>
              <view class="status-badge {{item.type}}">{{item.type === 'main' ? '服务' : '配送'}}</view>
            </view>
            
            <!-- 状态描述 -->
            <view class="timeline-desc" wx:if="{{item.current}}">
              {{item.type === 'main' ? '服务正在进行中' : '药品配送中'}}
            </view>
          </view>
          
          <!-- 连接线 -->
          <view class="timeline-line" wx:if="{{index < statusFlow.length - 1}}"></view>
        </view>
      </block>
    </view>
  </view>

  <!-- 配送信息卡片 -->
  <view class="delivery-card" wx:if="{{deliveryInfo}}">
    <view class="card-header">
      <t-icon name="location" size="24rpx" color="#0C4147" />
      <text class="card-title">配送信息</text>
    </view>
    
    <view class="delivery-content" bindtap="onDeliveryTap">
      <view class="delivery-item">
        <text class="item-label">配送地址：</text>
        <text class="item-value">{{deliveryInfo.address}}</text>
      </view>
      
      <view class="delivery-item" wx:if="{{deliveryInfo.contact}}">
        <text class="item-label">联系人：</text>
        <text class="item-value">{{deliveryInfo.contact}}</text>
      </view>
      
      <view class="delivery-item" wx:if="{{deliveryInfo.phone}}">
        <text class="item-label">联系电话：</text>
        <text class="item-value">{{deliveryInfo.phone}}</text>
      </view>
    </view>
  </view>

  <!-- 快捷操作 -->
  <view class="quick-actions">
    <view class="action-item" bindtap="onContactTap">
      <t-icon name="service" size="32rpx" color="#007AFF" />
      <text class="action-text">联系客服</text>
    </view>
    
    <view class="action-item" wx:if="{{deliveryInfo}}" bindtap="onDeliveryTap">
      <t-icon name="location" size="32rpx" color="#34C759" />
      <text class="action-text">查看配送</text>
    </view>
  </view>
</view> 