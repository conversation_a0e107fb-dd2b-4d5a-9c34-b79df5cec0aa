Component({
  properties: {
    // 主服务订单状态
    mainOrderStatus: {
      type: String,
      value: ''
    },
    // 药品子订单状态
    medicineOrderStatus: {
      type: String,
      value: ''
    },
    // 订单信息
    orderInfo: {
      type: Object,
      value: {}
    }
  },

  data: {
    // 状态流转数据
    statusFlow: [],
    // 当前状态描述
    currentStatusDesc: '',
    // 预计完成时间
    estimatedTime: '',
    // 配送信息
    deliveryInfo: null
  },

  lifetimes: {
    attached() {
      this.initStatusFlow();
    }
  },

  observers: {
    'mainOrderStatus, medicineOrderStatus, orderInfo': function() {
      this.initStatusFlow();
    }
  },

  methods: {
    // 初始化状态流转
    initStatusFlow() {
      const { mainOrderStatus, medicineOrderStatus, orderInfo } = this.data;
      
      // 生成状态流转数据
      const statusFlow = this.generateStatusFlow(mainOrderStatus, medicineOrderStatus);
      
      // 生成当前状态描述
      const currentStatusDesc = this.generateStatusDesc(mainOrderStatus, medicineOrderStatus);
      
      // 计算预计完成时间
      const estimatedTime = this.calculateEstimatedTime(mainOrderStatus, medicineOrderStatus);
      
      // 获取配送信息
      const deliveryInfo = this.getDeliveryInfo(orderInfo);
      
      this.setData({
        statusFlow,
        currentStatusDesc,
        estimatedTime,
        deliveryInfo
      });
    },

    // 生成状态流转数据
    generateStatusFlow(mainStatus, medicineStatus) {
      const flow = [];
      
      // 主服务订单状态
      const mainSteps = [
        { status: 'pending', label: '待处理', icon: 'time', color: '#999' },
        { status: 'paid', label: '已支付', icon: 'check-circle', color: '#34C759' },
        { status: 'assigned', label: '已分配', icon: 'user-circle', color: '#007AFF' },
        { status: 'in_progress', label: '服务中', icon: 'service', color: '#FF9500' },
        { status: 'completed', label: '已完成', icon: 'check', color: '#34C759' }
      ];
      
      // 药品配送状态
      const medicineSteps = [
        { status: 'pending', label: '待处理', icon: 'time', color: '#999' },
        { status: 'preparing', label: '配药中', icon: 'medicine-box', color: '#FF9500' },
        { status: 'shipped', label: '已发货', icon: 'truck', color: '#007AFF' },
        { status: 'delivered', label: '已送达', icon: 'check', color: '#34C759' }
      ];
      
      // 根据当前状态标记完成和当前状态
      mainSteps.forEach((step, index) => {
        const isCompleted = this.isStatusCompleted(mainStatus, step.status);
        const isCurrent = mainStatus === step.status;
        
        flow.push({
          ...step,
          type: 'main',
          completed: isCompleted,
          current: isCurrent,
          index: index
        });
      });
      
      // 如果主订单状态为in_progress，添加药品配送状态
      if (mainStatus === 'in_progress' && medicineStatus) {
        medicineSteps.forEach((step, index) => {
          const isCompleted = this.isMedicineStatusCompleted(medicineStatus, step.status);
          const isCurrent = medicineStatus === step.status;
          
          flow.push({
            ...step,
            type: 'medicine',
            completed: isCompleted,
            current: isCurrent,
            index: index + mainSteps.length
          });
        });
      }
      
      return flow;
    },

    // 生成状态描述
    generateStatusDesc(mainStatus, medicineStatus) {
      const descMap = {
        'pending': '您的订单已创建，我们正在为您安排服务',
        'paid': '订单已支付成功，正在为您分配服务人员',
        'assigned': '已为您分配服务人员，即将开始服务',
        'in_progress': medicineStatus ? this.getMedicineStatusDesc(medicineStatus) : '服务正在进行中',
        'completed': '服务已完成，感谢您的使用'
      };
      
      return descMap[mainStatus] || '订单处理中';
    },

    // 获取药品状态描述
    getMedicineStatusDesc(medicineStatus) {
      const descMap = {
        'pending': '正在为您准备药品',
        'preparing': '正在为您配药，请稍候',
        'shipped': '药品已发货，正在配送中',
        'delivered': '药品已送达，请注意查收'
      };
      
      return descMap[medicineStatus] || '药品配送中';
    },

    // 计算预计完成时间
    calculateEstimatedTime(mainStatus, medicineStatus) {
      const now = new Date();
      let estimatedMinutes = 0;
      
      // 根据当前状态计算预计时间
      if (mainStatus === 'pending') {
        estimatedMinutes = 30; // 30分钟内处理
      } else if (mainStatus === 'paid') {
        estimatedMinutes = 60; // 1小时内分配
      } else if (mainStatus === 'assigned') {
        estimatedMinutes = 120; // 2小时内开始服务
      } else if (mainStatus === 'in_progress') {
        if (medicineStatus === 'pending') {
          estimatedMinutes = 180; // 3小时内配药
        } else if (medicineStatus === 'preparing') {
          estimatedMinutes = 240; // 4小时内发货
        } else if (medicineStatus === 'shipped') {
          estimatedMinutes = 300; // 5小时内送达
        } else {
          estimatedMinutes = 60; // 1小时内完成
        }
      }
      
      if (estimatedMinutes > 0) {
        const estimatedTime = new Date(now.getTime() + estimatedMinutes * 60000);
        return this.formatTime(estimatedTime);
      }
      
      return '';
    },

    // 获取配送信息
    getDeliveryInfo(orderInfo) {
      if (orderInfo.deliveryInfo) {
        return {
          address: orderInfo.deliveryInfo.address,
          contact: orderInfo.deliveryInfo.contact,
          phone: orderInfo.deliveryInfo.phone
        };
      }
      return null;
    },

    // 判断状态是否已完成
    isStatusCompleted(currentStatus, targetStatus) {
      const statusOrder = {
        'pending': 0,
        'paid': 1,
        'assigned': 2,
        'in_progress': 3,
        'completed': 4
      };
      
      const currentOrder = statusOrder[currentStatus] || 0;
      const targetOrder = statusOrder[targetStatus] || 0;
      
      return currentOrder > targetOrder;
    },

    // 判断药品状态是否已完成
    isMedicineStatusCompleted(currentStatus, targetStatus) {
      const statusOrder = {
        'pending': 0,
        'preparing': 1,
        'shipped': 2,
        'delivered': 3
      };
      
      const currentOrder = statusOrder[currentStatus] || 0;
      const targetOrder = statusOrder[targetStatus] || 0;
      
      return currentOrder > targetOrder;
    },

    // 格式化时间
    formatTime(date) {
      const pad = n => n < 10 ? '0' + n : n;
      return `${date.getFullYear()}-${pad(date.getMonth() + 1)}-${pad(date.getDate())} ${pad(date.getHours())}:${pad(date.getMinutes())}`;
    },

    // 点击状态步骤
    onStepTap(e) {
      const { status, type, index } = e.currentTarget.dataset;
      this.triggerEvent('stepTap', { status, type, index });
    },

    // 点击配送信息
    onDeliveryTap() {
      const { deliveryInfo } = this.data;
      if (deliveryInfo) {
        this.triggerEvent('deliveryTap', deliveryInfo);
      }
    },

    // 点击联系客服
    onContactTap() {
      this.triggerEvent('contactTap');
    }
  }
}); 