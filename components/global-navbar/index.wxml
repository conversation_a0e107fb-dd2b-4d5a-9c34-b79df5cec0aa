<view class="global-navbar" style="padding-top: {{statusBarHeight}}px; background-color: {{backgroundColor}};">
  <view class="navbar-content" style="height: {{contentHeight}}px;">
    <!-- 左侧返回按钮 -->
    <view class="navbar-left" wx:if="{{showBack}}" bindtap="handleBack">
      <t-icon name="chevron-left" size="24" color="{{textColor}}" />
    </view>
    
    <!-- 中间标题 -->
    <view class="navbar-title" style="color: {{textColor}};">
      {{title || '页面标题'}}
    </view>
    
    <!-- 右侧内容插槽 -->
    <view class="navbar-right">
      <slot name="right"></slot>
    </view>
  </view>
</view> 