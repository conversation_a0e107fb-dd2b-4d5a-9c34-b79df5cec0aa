.global-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 999;
  /* 调试样式，确保导航栏可见 */
  min-height: 88rpx;
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32rpx;
  position: relative;
  /* 避免与胶囊按钮冲突 */
  padding-right: calc(32rpx + 87rpx); /* 32rpx 基础间距 + 胶囊按钮宽度 */
}

.navbar-left {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.05);
  transition: background-color 0.2s;
  flex-shrink: 0;
}

.navbar-left:active {
  background-color: rgba(0, 0, 0, 0.1);
}

.navbar-title {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  font-size: 36rpx;
  font-weight: 500;
  color: #000000;
  max-width: 400rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.navbar-right {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  min-width: 64rpx;
  flex-shrink: 0;
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
  .global-navbar {
    background-color: #1a1a1a;
  }
  
  .navbar-left {
    background-color: rgba(255, 255, 255, 0.1);
  }
  
  .navbar-left:active {
    background-color: rgba(255, 255, 255, 0.2);
  }
  
  .navbar-title {
    color: #ffffff;
  }
} 