Component({
  properties: {
    // 页面标题
    title: {
      type: String,
      value: ''
    },
    // 是否显示返回按钮
    showBack: {
      type: Boolean,
      value: true
    },
    // 文字颜色
    textColor: {
      type: String,
      value: '#000000'
    },
    // 背景颜色
    backgroundColor: {
      type: String,
      value: '#ffffff'
    },
    // 是否固定在顶部
    fixed: {
      type: Boolean,
      value: true
    },
    // 自定义导航栏高度（可选，如果不设置则自动计算）
    customHeight: {
      type: Number,
      value: 0
    },
    // 内容区域顶部间距（用于精确控制内容位置）
    contentTopMargin: {
      type: Number,
      value: 0
    }
  },

  data: {
    statusBarHeight: 0,
    navbarHeight: 0,
    contentTop: 0
  },

  lifetimes: {
    attached() {
      this.initNavbar();
    }
  },

  methods: {
    // 初始化导航栏
    initNavbar() {
      const systemInfo = wx.getSystemInfoSync();
      const menuButtonInfo = wx.getMenuButtonBoundingClientRect();
      
      // 状态栏高度
      const statusBarHeight = systemInfo.statusBarHeight;
      
      // 胶囊按钮信息
      const capsuleHeight = menuButtonInfo.height;
      const capsuleTop = menuButtonInfo.top;
      const capsuleRight = menuButtonInfo.right;
      const capsuleWidth = menuButtonInfo.width;
      
      // 导航栏内容区域高度
      const contentHeight = capsuleHeight;
      
      // 如果设置了自定义高度，使用自定义高度
      let navbarHeight;
      if (this.properties.customHeight > 0) {
        navbarHeight = this.properties.customHeight;
      } else {
        // 自动计算导航栏总高度 = 状态栏高度 + 内容区域高度 + 间距
        navbarHeight = statusBarHeight + contentHeight + (capsuleTop - statusBarHeight) * 2;
      }
      
      // 计算内容区域的顶部位置
      const contentTop = statusBarHeight + (capsuleTop - statusBarHeight);
      
      this.setData({
        statusBarHeight,
        navbarHeight,
        capsuleHeight,
        capsuleTop,
        capsuleRight,
        capsuleWidth,
        contentHeight,
        contentTop
      });
      
      // 触发事件，将导航栏信息传递给页面
      this.triggerEvent('navbarReady', {
        navbarHeight,
        contentTop,
        statusBarHeight
      });
    },

    // 返回按钮点击事件
    handleBack() {
      const pages = getCurrentPages();
      if (pages.length > 1) {
        wx.navigateBack({
          delta: 1
        });
      } else {
        // 如果是首页，跳转到首页
        wx.reLaunch({
          url: '/pages/home/<USER>'
        });
      }
    }
  }
}); 