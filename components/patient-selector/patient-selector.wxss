/* 卡片基础样式 */
.m3-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px 0 rgba(12, 65, 71, 0.08);
  padding: 32rpx 24rpx;
  margin-bottom: 0;
}

.m3-card-title {
  font-size: 24rpx;
  color: #0C4147;
  font-weight: 700;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.m3-card-title text {
  font-size: 24rpx;
  color: #0C4147;
  font-weight: 700;
}

.required {
  color: #BA1A1A;
  font-weight: 700;
}

/* 选择器样式 */
.m3-picker {
  background: #F5F5F5;
  border-radius: 8px;
  padding: 24rpx;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
  transition: all 0.3s ease;
  border: 1px solid rgba(12, 65, 71, 0.1);
  min-height: 88rpx;
  display: flex;
  align-items: center;
  line-height: 1.4;
}

.m3-picker:active {
  background: #E8E8E8;
  border-color: #0C4147;
}

/* 病患信息样式 */
.m3-patient-info {
  background: #F5F5F5;
  border-radius: 8px;
  padding: 20rpx 24rpx;
  margin-top: 16rpx;
  border: 1px solid rgba(12, 65, 71, 0.1);
}

.m3-info-row {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  font-size: 24rpx;
  line-height: 1.4;
  gap: 8rpx;
}

.m3-info-row:last-child {
  margin-bottom: 0;
}

.m3-info-label {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
  min-width: 120rpx;
  flex-shrink: 0;
}

.m3-info-input {
  flex: 1;
  background: #fff;
  border-radius: 6px;
  padding: 16rpx 20rpx;
  font-size: 24rpx;
  color: #333;
  border: 1px solid rgba(12, 65, 71, 0.1);
  outline: none;
  min-height: 72rpx;
  transition: all 0.3s ease;
}

.m3-info-input:focus {
  border-color: #0C4147;
  background: #fff;
}

.m3-info-input::placeholder {
  color: #999;
  font-size: 24rpx;
}

.m3-info-picker {
  flex: 1;
  background: #fff;
  border-radius: 6px;
  padding: 16rpx 20rpx;
  font-size: 24rpx;
  color: #333;
  border: 1px solid rgba(12, 65, 71, 0.1);
  min-height: 72rpx;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
}

.m3-info-picker:active {
  background: #E8E8E8;
  border-color: #0C4147;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .m3-card {
    padding: 24rpx 16rpx;
  }
  
  .m3-info-row {
    flex-direction: column;
    align-items: stretch;
    gap: 8rpx;
  }
  
  .m3-info-label {
    min-width: auto;
  }
} 