const { getPatientList } = require('../../api/patients.js');

Component({
  properties: {
    // 是否显示病患选择器
    showSelector: {
      type: Boolean,
      value: true
    },
    // 是否必填
    required: {
      type: Boolean,
      value: true
    }
  },

  data: {
    patients: [],
    selectedPatientIndex: null,
    selectedPatient: null,
    patientId: null,
    patientName: '',
    patientGender: '',
    patientGenderValue: '', // 用于提交的英文性别值
    patientIdCard: '',
    patientPhone: '',
    patientBirthday: ''
  },

  lifetimes: {
    attached() {
      this.loadPatients();
    }
  },

  methods: {
    // 加载病患列表
    loadPatients() {
      getPatientList().then(patients => {
        this.setData({ patients });
      }).catch(err => {
        wx.showToast({ title: '获取病人列表失败', icon: 'none' });
      });
    },

    // 选择预设病患
    onPatientPickerChange(e) {
      const index = Number(e.detail.value);
      const patient = this.data.patients[index];
      
      // 转换性别显示
      const genderDisplay = patient.gender === 'male' ? '男' : patient.gender === 'female' ? '女' : patient.gender;
      
      this.setData({
        selectedPatientIndex: index,
        selectedPatient: patient,
        patientId: patient.patient_id || '',
        patientName: patient.name || '',
        patientGender: genderDisplay,
        patientGenderValue: patient.gender || '', // 保存原始英文值
        patientIdCard: patient.id_card || '',
        patientPhone: patient.phone || '',
        patientBirthday: patient.birthday || ''
      });

      // 触发事件，通知父组件
      this.triggerEvent('patientChange', {
        patientId: this.data.patientId,
        patientName: this.data.patientName,
        patientGender: this.data.patientGender,
        patientGenderValue: this.data.patientGenderValue,
        patientIdCard: this.data.patientIdCard,
        patientPhone: this.data.patientPhone,
        patientBirthday: this.data.patientBirthday
      });
    },

    // 手动编辑病患信息字段
    onPatientNameInput(e) {
      this.setData({ patientName: e.detail.value });
      this.triggerPatientChange();
    },

    onPatientGenderChange(e) {
      const index = Number(e.detail.value);
      const genderArray = ['男', '女'];
      const genderValueArray = ['male', 'female']; // 对应的英文值
      const genderDisplay = genderArray[index];
      const genderValue = genderValueArray[index];
      this.setData({ 
        patientGender: genderDisplay, // 显示用中文
        patientGenderValue: genderValue // 提交用英文
      });
      this.triggerPatientChange();
    },

    onPatientIdCardInput(e) {
      this.setData({ patientIdCard: e.detail.value });
      this.triggerPatientChange();
    },

    onPatientPhoneInput(e) {
      this.setData({ patientPhone: e.detail.value });
      this.triggerPatientChange();
    },

    onPatientBirthdayChange(e) {
      this.setData({ patientBirthday: e.detail.value });
      this.triggerPatientChange();
    },

    // 触发病患信息变化事件
    triggerPatientChange() {
      this.triggerEvent('patientChange', {
        patientId: this.data.patientId,
        patientName: this.data.patientName,
        patientGender: this.data.patientGender,
        patientGenderValue: this.data.patientGenderValue,
        patientIdCard: this.data.patientIdCard,
        patientPhone: this.data.patientPhone,
        patientBirthday: this.data.patientBirthday
      });
    },

    // 获取病患信息（供父组件调用）
    getPatientInfo() {
      return {
        patientId: this.data.patientId,
        patientName: this.data.patientName,
        patientGender: this.data.patientGender,
        patientGenderValue: this.data.patientGenderValue,
        patientIdCard: this.data.patientIdCard,
        patientPhone: this.data.patientPhone,
        patientBirthday: this.data.patientBirthday
      };
    },

    // 验证病患信息
    validate() {
      if (!this.data.patientName || this.data.patientName.trim() === '') {
        return { isValid: false, message: '请输入病患姓名' };
      }
      if (!this.data.patientGenderValue || this.data.patientGenderValue.trim() === '') {
        return { isValid: false, message: '请选择病患性别' };
      }
      if (!this.data.patientIdCard || this.data.patientIdCard.trim() === '') {
        return { isValid: false, message: '请输入病患身份证号' };
      }
      if (!this.data.patientPhone || this.data.patientPhone.trim() === '') {
        return { isValid: false, message: '请输入病患手机号' };
      }
      if (!this.data.patientBirthday || this.data.patientBirthday.trim() === '') {
        return { isValid: false, message: '请选择病患生日' };
      }
      
      // 验证身份证号格式
      const idCardRegex = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
      if (!idCardRegex.test(this.data.patientIdCard)) {
        return { isValid: false, message: '身份证号格式不正确' };
      }
      
      // 验证手机号格式
      const phoneRegex = /^1[3-9]\d{9}$/;
      if (!phoneRegex.test(this.data.patientPhone)) {
        return { isValid: false, message: '手机号格式不正确' };
      }

      return { isValid: true, message: '验证通过' };
    }
  }
}); 