<view class="m3-card">
  <view class="m3-card-title">
    <t-icon name="user" size="24rpx" color="#0C4147" />
    <text>病患信息</text>
    <text class="required" wx:if="{{required}}">*</text>
  </view>
  
  <!-- 病患选择器 -->
  <picker wx:if="{{showSelector}}" class="m3-patient-picker" range="{{patients}}" range-key="name" value="{{selectedPatientIndex}}" bindchange="onPatientPickerChange">
    <view class="m3-picker">{{selectedPatient ? selectedPatient.name : '请选择病人（可选）'}}</view>
  </picker>
  
  <!-- 病患信息表单 -->
  <view class="m3-patient-info">
    <view class="m3-info-row">
      <text class="m3-info-label">姓名</text>
      <text class="required" wx:if="{{required}}">*</text>
      <input class="m3-info-input" placeholder="请输入病患姓名" value="{{patientName}}" bindinput="onPatientNameInput" />
    </view>
    
    <view class="m3-info-row">
      <text class="m3-info-label">性别</text>
      <text class="required" wx:if="{{required}}">*</text>
      <picker mode="selector" range="{{['男', '女']}}" value="{{patientGender === '男' ? 0 : 1}}" bindchange="onPatientGenderChange">
        <view class="m3-info-picker">{{patientGender || '请选择性别'}}</view>
      </picker>
    </view>
    
    <view class="m3-info-row">
      <text class="m3-info-label">身份证号</text>
      <text class="required" wx:if="{{required}}">*</text>
      <input class="m3-info-input" placeholder="请输入身份证号" value="{{patientIdCard}}" bindinput="onPatientIdCardInput" maxlength="18" />
    </view>
    
    <view class="m3-info-row">
      <text class="m3-info-label">手机号</text>
      <text class="required" wx:if="{{required}}">*</text>
      <input class="m3-info-input" placeholder="请输入手机号" value="{{patientPhone}}" bindinput="onPatientPhoneInput" maxlength="11" type="number" />
    </view>
    
    <view class="m3-info-row">
      <text class="m3-info-label">生日</text>
      <text class="required" wx:if="{{required}}">*</text>
      <picker mode="date" value="{{patientBirthday}}" bindchange="onPatientBirthdayChange">
        <view class="m3-info-picker">{{patientBirthday || '请选择生日'}}</view>
      </picker>
    </view>
  </view>
</view> 