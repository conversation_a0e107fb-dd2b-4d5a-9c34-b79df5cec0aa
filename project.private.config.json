{"libVersion": "3.9.2", "projectname": "med_wx", "setting": {"urlCheck": true, "coverView": true, "lazyloadPlaceholderEnable": false, "skylineRenderEnable": true, "preloadBackgroundData": false, "autoAudits": false, "useApiHook": true, "useApiHostProcess": true, "showShadowRootInWxmlPanel": true, "useStaticServer": false, "useLanDebug": false, "showES6CompileOption": false, "compileHotReLoad": true, "bigPackageSizeSupport": true, "checkInvalidKey": true, "useIsolateContext": true, "ignoreDevUnusedFiles": true}, "condition": {"miniprogram": {"list": [{"name": "pages/packages/list", "pathName": "pages/packages/list", "query": "", "scene": null, "launchMode": "default"}]}}, "description": "项目私有配置文件。此文件中的内容将覆盖 project.config.json 中的相同字段。项目的改动优先同步到此文件中。详见文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/projectconfig.html"}