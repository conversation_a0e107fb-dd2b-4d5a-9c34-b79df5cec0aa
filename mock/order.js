module.exports = {
  orders: [
    {
      order_id: 201,
      order_no: "ORD20240601001",
      user_id: 1,
      staff_id: 1,
      hospital_id: 1,
      service_type: "companion",
      service_details: { items: [{ type: "挂号", desc: "协和医院挂号" }], requirements: "陪同就诊" },
      status: "pending",
      appointment_time: "2024-06-01T09:00:00Z",
      duration: 60,
      price: 200.00,
      payment_info: { method: "微信", status: "未支付" },
      patient_info: { name: "张三", age: 30, condition: "高血压" },
      location_info: { address: "协和医院门诊楼", room: "101" },
      rating: 5,
      review: "服务很好",
      attachments: ["https://example.com/order201-1.png"],
      created_at: "2024-06-01T08:00:00Z",
      updated_at: "2024-06-01T08:30:00Z",
      completed_at: null
    },
    {
      order_id: 202,
      order_no: "ORD20240601002",
      user_id: 2,
      staff_id: 2,
      hospital_id: 2,
      service_type: "registration",
      service_details: { items: [{ type: "挂号", desc: "人民医院挂号" }], requirements: "挂号服务" },
      status: "paid",
      appointment_time: "2024-06-02T10:00:00Z",
      duration: 30,
      price: 50.00,
      payment_info: { method: "支付宝", status: "已支付" },
      patient_info: { name: "李四", age: 40, condition: "糖尿病" },
      location_info: { address: "人民医院门诊楼", room: "202" },
      rating: 4,
      review: "流程顺畅",
      attachments: [],
      created_at: "2024-06-02T08:00:00Z",
      updated_at: "2024-06-02T08:30:00Z",
      completed_at: "2024-06-02T11:00:00Z"
    }
  ]
}; 