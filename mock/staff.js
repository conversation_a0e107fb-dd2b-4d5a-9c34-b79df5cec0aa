module.exports = {
  medical_staff: [
    {
      staff_id: 1,
      user_id: 1,
      hospital_id: 1,
      type: "doctor",
      qualifications: { certificates: [{ name: "医师资格证", url: "https://example.com/cert1.png" }] },
      specialties: ["内科"],
      service_score: 4.9,
      service_count: 120,
      introduction: "擅长内科常见病诊治。",
      service_area: [1, 2],
      service_price: { standard: 100, overnight: 200 },
      is_verified: true,
      available_days: { weekdays: [1, 3, 5], start: "09:00", end: "18:00" },
      created_at: "2024-01-01T10:00:00Z",
      updated_at: "2024-01-01T10:00:00Z"
    },
    {
      staff_id: 2,
      user_id: 2,
      hospital_id: 2,
      type: "companion",
      qualifications: { certificates: [{ name: "陪诊师证", url: "https://example.com/cert2.png" }] },
      specialties: ["陪诊"],
      service_score: 4.7,
      service_count: 80,
      introduction: "专业陪诊服务。",
      service_area: [2, 3],
      service_price: { standard: 80, overnight: 150 },
      is_verified: false,
      available_days: { weekdays: [2, 4], start: "10:00", end: "17:00" },
      created_at: "2024-02-01T10:00:00Z",
      updated_at: "2024-02-01T10:00:00Z"
    }
  ]
}; 