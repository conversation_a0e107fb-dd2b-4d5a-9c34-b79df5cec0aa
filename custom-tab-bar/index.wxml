<view class="tab-bar">
  <view class="tab-bar-content">
    <view class="tab-bar-border"></view>
    <block wx:if="{{userType === 'staff' || userType === 'medical_staff'}}">
      <!-- Staff用户菜单 -->
      <view wx:for="{{staffTabs}}" wx:key="index" class="tab-bar-item {{selected === index ? 'selected' : ''}}" data-path="{{item.pagePath}}" data-index="{{index}}" bindtap="switchTab">
        <view class="tab-bar-item-wrapper">
          <t-icon 
            wx:if="{{item.iconType === 'tdesign'}}"
            name="{{selected === index ? item.selectedIcon : item.icon}}" 
            size="48rpx" 
            color="{{selected === index ? selectedColor : color}}"
            class="tab-bar-item-icon"
          />
          <text wx:else class="tab-bar-item-icon">{{selected === index ? item.selectedIcon : item.icon}}</text>
          <view style="color: {{selected === index ? selectedColor : color}}" class="tab-bar-item-text">{{item.text}}</view>
          <!-- 消息tab的红点显示 -->
          <view wx:if="{{index === 2 && unreadCount > 0}}" class="tab-bar-red-dot">
            <text wx:if="{{unreadCount <= 99}}" class="red-dot-text">{{unreadCount}}</text>
            <text wx:else class="red-dot-text">99+</text>
          </view>
        </view>
      </view>
    </block>
    <block wx:else>
      <!-- 普通用户菜单 -->
      <view wx:for="{{patientTabs}}" wx:key="index" class="tab-bar-item {{selected === index ? 'selected' : ''}}" data-path="{{item.pagePath}}" data-index="{{index}}" bindtap="switchTab">
        <view class="tab-bar-item-wrapper">
          <t-icon 
            wx:if="{{item.iconType === 'tdesign'}}"
            name="{{selected === index ? item.selectedIcon : item.icon}}" 
            size="48rpx" 
            color="{{selected === index ? selectedColor : color}}"
            class="tab-bar-item-icon"
          />
          <text wx:else class="tab-bar-item-icon">{{selected === index ? item.selectedIcon : item.icon}}</text>
          <view style="color: {{selected === index ? selectedColor : color}}" class="tab-bar-item-text">{{item.text}}</view>
          <!-- 消息tab的红点显示 -->
          <view wx:if="{{index === 2 && unreadCount > 0}}" class="tab-bar-red-dot">
            <text wx:if="{{unreadCount <= 99}}" class="red-dot-text">{{unreadCount}}</text>
            <text wx:else class="red-dot-text">99+</text>
          </view>
        </view>
      </view>
    </block>
  </view>
</view> 