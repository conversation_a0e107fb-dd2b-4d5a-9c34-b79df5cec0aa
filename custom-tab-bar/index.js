Component({
  data: {
    selected: 0,
    color: "rgba(255, 255, 255, 0.6)",
    selectedColor: "#ffffff",
    userType: '',
    unreadCount: 0, // 添加未读消息数量
    // 普通用户菜单
    patientTabs: [
      {
        pagePath: "pages/home/<USER>",
        text: "首页",
        icon: "home",
        selectedIcon: "home-filled",
        iconType: "tdesign"
      },
      // {
      //   pagePath: "pages/service/service",
      //   text: "服务",
      //   icon: "🛠️",
      //   selectedIcon: "🛠️"
      // },
      {
        pagePath: "pages/orders/orders",
        text: "订单",
        icon: "list",
        selectedIcon: "list",
        iconType: "tdesign"
      },
      {
        pagePath: "pages/message/message",
        text: "消息",
        icon: "chat",
        selectedIcon: "chat-filled",
        iconType: "tdesign"
      },
      {
        pagePath: "pages/mine/mine",
        text: "我的",
        icon: "user",
        selectedIcon: "user-filled",
        iconType: "tdesign"
      }
    ],
    // staff用户菜单
    staffTabs: [
      {
        pagePath: "pages/staff/dashboard/dashboard",
        text: "工作台",
        icon: "dashboard",
        selectedIcon: "dashboard-filled",
        iconType: "tdesign"
      },
      {
        pagePath: "pages/orders/orders",
        text: "订单",
        icon: "list",
        selectedIcon: "list",
        iconType: "tdesign"
      },
      {
        pagePath: "pages/message/message",
        text: "消息",
        icon: "chat",
        selectedIcon: "chat-filled",
        iconType: "tdesign"
      },
      {
        pagePath: "pages/mine/mine",
        text: "我的",
        icon: "user",
        selectedIcon: "user-filled",
        iconType: "tdesign"
      }
    ]
  },

  lifetimes: {
    attached() {
      this.getUserType();
      this.updateUnreadCount();
      // 监听消息更新事件
      this._onMessagesUpdated = (messages, unreadCount) => {
        this.setData({ unreadCount });
      };
      this._onTabBarRedDotUpdate = (unreadCount) => {
        this.setData({ unreadCount });
      };
      
      // 通过全局事件总线监听
      const app = getApp();
      if (app && app.globalData && app.globalData.eventBus) {
        app.globalData.eventBus.on('messagesUpdated', this._onMessagesUpdated);
        app.globalData.eventBus.on('tabBarRedDotUpdate', this._onTabBarRedDotUpdate);
      }
    },
    
    detached() {
      // 清理事件监听
      const app = getApp();
      if (app && app.globalData && app.globalData.eventBus) {
        if (this._onMessagesUpdated) {
          app.globalData.eventBus.off('messagesUpdated', this._onMessagesUpdated);
        }
        if (this._onTabBarRedDotUpdate) {
          app.globalData.eventBus.off('tabBarRedDotUpdate', this._onTabBarRedDotUpdate);
        }
      }
    }
  },

  methods: {
    getUserType() {
      const userInfo = wx.getStorageSync('userInfo') || {};
      this.setData({
        userType: userInfo.user_type || 'patient'
      });
    },

    updateUnreadCount() {
      // 从全局数据获取未读消息数量
      const app = getApp();
      if (app && app.globalData && app.globalData.unreadCount !== undefined) {
        this.setData({ unreadCount: app.globalData.unreadCount });
      } else {
        // 如果没有全局数据，默认为0
        this.setData({ unreadCount: 0 });
      }
    },

    switchTab(e) {
      const data = e.currentTarget.dataset;
      const url = data.path;
      
      // 根据用户类型确定当前选中的tab
      const tabs = this.data.userType === 'staff' || this.data.userType === 'medical_staff' 
        ? this.data.staffTabs 
        : this.data.patientTabs;
      
      const selected = tabs.findIndex(tab => tab.pagePath === url);
      
      this.setData({
        selected
      });
      
      wx.switchTab({
        url: `/${url}`
      });
    }
  }
}); 