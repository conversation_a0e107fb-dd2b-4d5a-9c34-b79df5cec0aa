.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120rpx;
  background: transparent;
  display: flex;
  padding: 0 24rpx 24rpx 24rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  z-index: 1000;
}

.tab-bar-content {
  background: #0C4147;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(12, 65, 71, 0.12);
  display: flex;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.tab-bar-border {
  background-color: rgba(12, 65, 71, 0.08);
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 1rpx;
  transform: scaleY(0.5);
}

.tab-bar-item {
  flex: 1;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  position: relative;
  padding: 16rpx 0;
  transition: all 0.2s ease;
}

.tab-bar-item:active {
  background-color: rgba(255, 255, 255, 0.1);
}

.tab-bar-item-wrapper {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.tab-bar-item-icon {
  font-size: 48rpx;
  margin-bottom: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  color: rgba(255, 255, 255, 0.6);
}

/* 确保 TDesign 图标居中显示 */
.tab-bar-item-icon t-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.tab-bar-item-text {
  font-size: 20rpx;
  font-weight: 500;
  transition: all 0.2s ease;
  color: rgba(255, 255, 255, 0.6);
}

/* 选中状态样式 */
.tab-bar-item.selected .tab-bar-item-icon {
  transform: scale(1.1);
  color: #ffffff;
}

.tab-bar-item.selected .tab-bar-item-text {
  font-weight: 600;
  color: #ffffff;
}

/* 红点样式 */
.tab-bar-red-dot {
  position: absolute;
  top: 8rpx;
  right: 50%;
  transform: translateX(50%);
  background: #ff4d4f;
  border-radius: 16rpx;
  min-width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 8rpx;
  box-sizing: border-box;
  z-index: 10;
}

.red-dot-text {
  color: white;
  font-size: 20rpx;
  font-weight: 600;
  line-height: 1;
} 