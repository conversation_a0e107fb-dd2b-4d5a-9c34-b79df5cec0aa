{"darkmode": true, "pages": ["pages/home/<USER>", "pages/button/button", "pages/button/skyline/button", "pages/tabs/tabs", "pages/icon/icon", "pages/icon/skyline/icon", "pages/loading/loading", "pages/loading/skyline/loading", "pages/progress/progress", "pages/progress/skyline/progress", "pages/cascader/cascader", "pages/cell/cell", "pages/cell/skyline/cell", "pages/cell-group/cell-group", "pages/collapse/collapse", "pages/input/input", "pages/input/skyline/input", "pages/badge/badge", "pages/badge/skyline/badge", "pages/textarea/textarea", "pages/textarea/skyline/textarea", "pages/message/message", "pages/toast/toast", "pages/toast/skyline/toast", "pages/stepper/stepper", "pages/stepper/skyline/stepper", "pages/slider/slider", "pages/slider/skyline/slider", "pages/radio/radio", "pages/radio/skyline/radio", "pages/switch/switch", "pages/switch/skyline/switch", "pages/sticky/sticky", "pages/tag/tag", "pages/tag/skyline/tag", "pages/checkbox/checkbox", "pages/checkbox/skyline/checkbox", "pages/gulp-error/index", "pages/fab/fab", "pages/fab/skyline/fab", "pages/tab-bar/tab-bar", "pages/tab-bar/skyline/tab-bar", "pages/transition/transition", "pages/popup/popup", "pages/popup/skyline/popup", "pages/steps/steps", "pages/steps/skyline/steps", "pages/dropdown-menu/dropdown-menu", "pages/drawer/drawer", "pages/drawer/skyline/drawer", "pages/pull-down-refresh/pull-down-refresh", "pages/pull-down-refresh/skyline/pull-down-refresh", "pages/skeleton/skeleton", "pages/skeleton/skyline/skeleton", "pages/footer/footer", "pages/footer/skyline/footer", "pages/divider/divider", "pages/divider/skyline/divider", "pages/empty/empty", "pages/empty/skyline/empty", "pages/back-top/back-top", "pages/back-top/skyline/back-top", "pages/grid/grid", "pages/upload/upload", "pages/count-down/count-down", "pages/count-down/skyline/count-down", "pages/overlay/overlay", "pages/overlay/skyline/overlay", "pages/image/image", "pages/image/skyline/image", "pages/search/search", "pages/search/skyline/search", "pages/home/<USER>/navigateFail", "pages/navbar/navbar", "pages/navbar/skyline/navbar", "pages/date-time-picker/date-time-picker", "pages/date-time-picker/skyline/date-time-picker", "pages/notice-bar/notice-bar", "pages/image-viewer/image-viewer", "pages/image-viewer/skyline/image-viewer", "pages/result/result", "pages/result/skyline/result", "pages/result/result-page", "pages/link/link", "pages/link/skyline/link", "pages/col/col", "pages/col/skyline/col", "pages/color-picker/color-picker", "pages/guide/guide", "pages/accompany/accompany", "pages/register/register", "pages/hospitalize/hospitalize", "pages/medicine-mall/medicine-mall", "pages/orders/orders", "pages/orders/detail", "pages/medicine-order/medicine-order", "pages/diary/list", "pages/news/list", "pages/services/list", "pages/service-flow/service-flow", "pages/proxy-service/proxy-service", "pages/example/example", "pages/test-navbar/test-navbar", "pages/diary/detail", "pages/diary/edit", "pages/diary/report", "pages/diary/archive", "pages/message/center", "pages/mine/mine", "pages/login/login", "pages/profile/profile", "pages/address/address", "pages/payment/payment", "pages/service/service", "pages/staff/list", "pages/staff/center", "pages/staff/detail", "pages/staff/dashboard/dashboard", "pages/hospital-list/hospital-list", "pages/hospital-detail/hospital-detail", "pages/review/review", "pages/review/list", "pages/user-register/user-register", "pages/paytest/paytest", "pages/patients/patients", "pages/packages/list", "pages/packages/detail"], "subpackages": [{"root": "pages/side-bar/", "pages": ["side-bar", "base/index", "switch/index", "custom/index", "with-icon/index"]}, {"root": "pages/action-sheet/", "pages": ["action-sheet"]}, {"root": "pages/avatar/", "pages": ["avatar", "skyline/avatar"]}, {"root": "pages/calendar/", "pages": ["calendar"]}, {"root": "pages/dialog/", "pages": ["dialog", "skyline/dialog"]}, {"root": "pages/picker/", "pages": ["picker", "skyline/picker"]}, {"root": "pages/rate/", "pages": ["rate"]}, {"root": "pages/swiper/", "pages": ["swiper", "skyline/swiper"]}, {"root": "pages/swipe-cell/", "pages": ["swipe-cell"]}, {"root": "pages/tree-select/", "pages": ["tree-select"]}, {"root": "pages/indexes/", "pages": ["indexes", "base/index", "custom/index"]}], "themeLocation": "theme.json", "usingComponents": {"t-demo": "./components/demo-block/index", "t-demo-header": "./components/demo-header/index", "t-button": "tdesign-miniprogram/button/button", "t-icon": "tdesign-miniprogram/icon/icon", "t-navbar": "tdesign-miniprogram/navbar/navbar", "global-navbar": "./components/global-navbar/index"}, "window": {"navigationBarTitleText": "TDesign", "navigationStyle": "custom", "navigationBarTextStyle": "@navTxtStyle"}, "plugins": {"routePlan": {"version": "2.0.3", "provider": "wx50b5593e81dd937a"}}, "permission": {"scope.userLocation": {"desc": "用于根据医院名称进行地图导航与定位"}}, "resolveAlias": {"@behaviors/*": "behaviors/*"}, "sitemapLocation": "sitemap.json", "lazyCodeLoading": "requiredComponents", "rendererOptions": {"skyline": {"disableABTest": true, "defaultDisplayBlock": true, "defaultContentBox": true, "sdkVersionBegin": "3.4.3", "sdkVersionEnd": "15.255.255"}}, "tabBar": {"color": "rgba(255, 255, 255, 0.6)", "selectedColor": "#ffffff", "backgroundColor": "#0C4147", "borderStyle": "black", "custom": true, "list": [{"pagePath": "pages/home/<USER>", "text": "首页"}, {"pagePath": "pages/staff/dashboard/dashboard", "text": "工作台"}, {"pagePath": "pages/orders/orders", "text": "订单"}, {"pagePath": "pages/message/message", "text": "消息"}, {"pagePath": "pages/mine/mine", "text": "我的"}]}}