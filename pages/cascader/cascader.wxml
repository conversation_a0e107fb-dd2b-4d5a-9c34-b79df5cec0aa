<t-navbar title="Cascader" leftArrow />
<view class="demo">
  <t-demo-header title="Cascader 级联选择器" desc="用于多层级数据的逐级选择。" notice="渲染框架支持情况：WebView" />
  <t-demo title="01 类型" desc="">
    <base />
  </t-demo>
  <t-demo desc="选项卡风格">
    <theme-tab />
  </t-demo>

  <t-demo title="02 进阶" desc="带初始值">
    <with-value />
  </t-demo>

  <t-demo desc="自定义 keys">
    <keys />
  </t-demo>

  <t-demo desc="使用次级标题">
    <with-title />
  </t-demo>

  <t-demo desc="选择任意一项">
    <check-strictly />
  </t-demo>
</view>
