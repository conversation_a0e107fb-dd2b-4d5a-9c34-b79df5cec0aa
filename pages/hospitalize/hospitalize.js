import navbarBehavior from '../../behaviors/navbar';
const auth = require('../../utils/auth.js');
const hospitalsApi = require('../../api/hospitals.js');
const medicalStaffApi = require('../../api/medical_staff.js');
const ordersApi = require('../../api/orders.js');
import { getHospitalList } from '../../utils/hospital.js';
const { API_BASE } = require('../../api/base.js');

Page({
  behaviors: [navbarBehavior],
  data: {
    hospitals: [],
    hospitalIndex: 0,
    daysOptions: [1,2,3,4,5,6,7],
    days: 1,
    demand: '',
    staffList: [],
    selectedStaffId: null,
    agree: false,
    showResult: false,
    orderNo: '',
    status: '',
    loading: false,
    staffLoading: false,
    // 病患信息（从组件获取）
    patientInfo: {
      patientId: null,
      patientName: '',
      patientGender: '',
      patientGenderValue: '',
      patientIdCard: '',
      patientPhone: '',
      patientBirthday: ''
    },
    locationInfo: { address: '', room: '' },
    date: '',
    // 套餐相关
    packageId: null,
    packageType: null,
    packageInfo: null,
  },
  
  onLoad(options) {
    console.log('[hospitalize] 页面 onLoad, options:', options);
    console.log('[hospitalize] 开始检查登录状态');
    // 检查登录状态并显示注册提示
    const result = auth.checkLoginAndShowRegisterPrompt();
    console.log('[hospitalize] 登录检查结果:', result);
    if (!result) {
      console.log('[hospitalize] 登录检查失败，返回');
      return;
    }
    console.log('[hospitalize] 登录检查通过，继续执行');

    // 获取套餐ID和类型
    const packageId = options.packageId;
    const type = options.type;
    if (packageId) {
      console.log('[hospitalize] 套餐预约模式，套餐ID:', packageId, '类型:', type);
      this.setData({ packageId, packageType: type });
      // 如果有套餐ID，加载套餐详情
      this.loadPackageInfo(packageId);
    }

    // 加载医院列表
    this.loadHospitals();
  },

  // 加载套餐信息
  loadPackageInfo(packageId) {
    const { getPackageDetail } = require('../../api/packages.js');
    getPackageDetail(packageId).then(packageInfo => {
      console.log('[hospitalize] 获取套餐详情成功:', packageInfo);
      this.setData({ packageInfo });
    }).catch(err => {
      console.error('[hospitalize] 获取套餐详情失败:', err);
      wx.showToast({
        title: '获取套餐信息失败',
        icon: 'none'
      });
    });
  },

  async loadHospitals() {
    this.setData({ loading: true });
    const hospitals = await getHospitalList();
    this.setData({ hospitals, loading: false });
    if (hospitals.length > 0) {
      this.loadStaffList(hospitals[0].id);
    }
  },
  
  // 加载陪护人员列表，hospitalId为必传
  loadStaffList(hospitalId) {
    this.setData({ staffLoading: true });
    medicalStaffApi.getMedicalStaff({
      page: 1,
      limit: 1000,
      hospitalId
    })
      .then((res) => {
        const staffList = res.data && res.data.data && res.data.data.medicalStaff ? res.data.data.medicalStaff : [];
        this.setData({ 
          staffList,
          staffLoading: false 
        });
      })
      .catch((err) => {
        wx.showToast({ title: '获取陪护人员失败', icon: 'none' });
        this.setData({ staffLoading: false });
      });
  },
  
  onHospitalChange(e) {
    const hospitalIndex = Number(e.detail.value);
    this.setData({ hospitalIndex });
    const hospitalId = this.data.hospitals[hospitalIndex].id;
    this.loadStaffList(hospitalId);
  },
  
  onDaysChange(e) {
    const idx = Number(e.detail.value);
    this.setData({ days: this.data.daysOptions[idx] });
  },
  
  onDemandInput(e) {
    const demand = e.detail.value;
    this.setData({ demand: demand });
    
    // 实时验证特殊需求长度
    if (demand && demand.trim().length > 500) {
      wx.showToast({ title: '特殊需求不能超过500个字符', icon: 'none' });
    }
  },
  
  onStaffSelect(e) {
    this.setData({ selectedStaffId: e.currentTarget.dataset.id });
  },
  
  onAgreeChange(e) {
    this.setData({ agree: !this.data.agree });
  },
  
  // 病患信息变化事件处理
  onPatientChange(e) {
    const patientInfo = e.detail;
    this.setData({ patientInfo });
  },
  onDateChange(e) {
    this.setData({ date: e.detail.value });
  },
  onLocationAddressInput(e) {
    const address = e.detail.value;
    this.setData({ locationInfo: { ...this.data.locationInfo, address: address } });
    
    // 实时验证地址长度
    if (address && address.trim().length > 200) {
      wx.showToast({ title: '地址不能超过200个字符', icon: 'none' });
    }
  },
  onLocationRoomInput(e) {
    const room = e.detail.value;
    this.setData({ locationInfo: { ...this.data.locationInfo, room: room } });
    
    // 实时验证房间号格式
    if (room && room.trim() !== '') {
      if (room.trim().length > 20) {
        wx.showToast({ title: '房间号不能超过20个字符', icon: 'none' });
      } else {
        const roomRegex = /^[0-9A-Za-z\-_\u4e00-\u9fa5]+$/;
        if (!roomRegex.test(room.trim())) {
          wx.showToast({ title: '房间号格式不正确', icon: 'none' });
        }
      }
    }
  },
  // onChooseLocation() {
  //   // 先检查位置权限
  //   wx.getSetting({
  //     success: (res) => {
  //       if (res.authSetting['scope.userLocation'] === false) {
  //         // 用户之前拒绝了位置权限，引导用户开启
  //         wx.showModal({
  //           title: '需要位置权限',
  //           content: '选择陪护地点需要获取您的位置信息，请在设置中开启位置权限',
  //           confirmText: '去设置',
  //           success: (modalRes) => {
  //             if (modalRes.confirm) {
  //               wx.openSetting();
  //             }
  //           }
  //         });
  //         return;
  //       }
        
  //       // 调用地图选点
  //       wx.chooseLocation({
  //         success: (res) => {
  //           console.log('选点成功:', res);
  //           this.setData({
  //             locationInfo: { ...this.data.locationInfo, address: res.address + (res.name ? '（' + res.name + '）' : '') }
  //           });
  //           wx.showToast({ title: '地点选择成功', icon: 'success' });
  //         },
  //         fail: (err) => {
  //           console.log('选点失败:', err);
  //           if (err.errMsg && err.errMsg.includes('cancel')) {
  //             // 用户取消，不提示
  //             return;
  //           }
  //           wx.showToast({ title: '选点失败，请重试', icon: 'none' });
  //         }
  //       });
  //     }
  //   });
  // },
  
  onSubmit() {
    // 基本表单校验
    const validationResult = this.validateForm();
    if (!validationResult.isValid) {
      wx.showToast({ title: validationResult.message, icon: 'none' });
      return;
    }
    const selectedHospital = this.data.hospitals[this.data.hospitalIndex];
    const selectedStaff = this.data.staffList.find(staff => staff.id === this.data.selectedStaffId);
    const userInfo = wx.getStorageSync('userInfo') || {};
    if (!selectedHospital || !selectedStaff) {
      wx.showToast({ title: '数据错误', icon: 'none' });
      return;
    }
    // 获取价格：优先使用套餐价格，否则使用陪护人员价格
    let price;
    if (this.data.packageInfo && this.data.packageInfo.price) {
      price = Number(this.data.packageInfo.price);
      console.log('[hospitalize] 使用套餐价格:', price);
    } else {
      price = selectedStaff.servicePrice.standard * this.data.days;
      console.log('[hospitalize] 使用陪护人员价格:', price);
    }
    // 构建病患信息
    const patientInfo = this.data.patientInfo;
    const patientId = patientInfo.patientId || null;
    const patientName = patientInfo.patientName || '';
    const patientGender = patientInfo.patientGender || '';
    const patientGenderValue = patientInfo.patientGenderValue || ''; // 用于提交的英文性别值
    const patientIdCard = patientInfo.patientIdCard || '';
    const patientPhone = patientInfo.patientPhone || '';
    const patientBirthday = patientInfo.patientBirthday || '';
    
    // 组装与陪诊一致的 payload
    let payload = {
      serviceType: 'hospitalization',
      status: 'pending',
      price,
      remarks: this.data.demand || '',
      userId: userInfo.id,
      staffId: selectedStaff.id,
      hospitalId: selectedHospital.id,
      locationInfo: this.data.locationInfo || { address: '', room: '' },
      appointmentTime: this.data.date ? `${this.data.date}T00:00:00.000Z` : '',
      duration: this.data.days || 1,
      serviceDetails: {
        items: [{
          type: 'hospitalization',
          staffId: selectedStaff.id,
          desc: this.data.demand || '住院陪护服务'
        }],
        requirements: this.data.demand || ''
      },
      // 添加套餐信息
      packageId: this.data.packageId || null,
      packageType: this.data.packageType || null
    };
    
    if (patientId) {
      // 如果有预设病患ID，传递patientId和patientInfo
      payload.patientId = patientId;
      payload.patientInfo = {
        name: patientName,
        gender: patientGenderValue, // 使用英文性别值
        idCard: patientIdCard,
        phone: patientPhone,
        birthday: patientBirthday
      };
    } else {
      // 如果没有预设病患ID，只传递patientInfo
      payload.patientInfo = {
        name: patientName,
        gender: patientGenderValue, // 使用英文性别值
        idCard: patientIdCard,
        phone: patientPhone,
        birthday: patientBirthday
      };
    }
    
    // 调试信息
    console.log('提交的payload:', payload);
    console.log('病患信息:', payload.patientInfo);
    
    auth.requestWithToken({
      url: `${API_BASE}/orders/service_orders`,
      method: 'POST',
      data: payload,
      success: (res) => {
        if (res.data && res.data.success) {
          wx.showToast({ title: '下单成功', icon: 'success', duration: 1200 });
          this.setData({ showResult: true, orderNo: res.data.data && res.data.data.orderNo, status: '已下单' });
        } else {
          const msg = (res.data && (res.data.message || (res.data.errors && res.data.errors[0] && res.data.errors[0].msg))) || '下单失败';
          wx.showToast({ title: msg, icon: 'none', duration: 2000 });
        }
      },
      fail: (err) => {
        wx.showToast({ title: '网络错误', icon: 'none' });
      }
    });
  },
  
  onCloseResult() {
    this.setData({ showResult: false });
    // 添加短暂延迟，确保订单数据已保存
    wx.showLoading({ title: '跳转中...' });
    setTimeout(() => {
      wx.hideLoading();
      // 跳转到订单页面
      wx.switchTab({ url: '/pages/orders/orders' });
    }, 500);
  },

  // 表单验证函数
  validateForm() {
    const {
      hospitals,
      hospitalIndex,
      days,
      date,
      patientInfo,
      selectedStaffId,
      locationInfo,
      agree
    } = this.data;

    // 医院验证
    if (!hospitals || hospitals.length === 0) {
      return { isValid: false, message: '暂无可用医院' };
    }
    if (hospitalIndex < 0 || hospitalIndex >= hospitals.length) {
      return { isValid: false, message: '请选择医院' };
    }

    // 陪护天数验证
    if (!days || days < 1) {
      return { isValid: false, message: '请选择陪护天数' };
    }
    if (days > 30) {
      return { isValid: false, message: '陪护天数不能超过30天' };
    }

    // 预约日期验证
    if (!date || date.trim() === '') {
      return { isValid: false, message: '请选择预约日期' };
    }

    // 验证日期是否为今天或未来日期
    const selectedDate = new Date(date);
    const today = new Date();
    today.setHours(0, 0, 0, 0); // 设置为今天的开始时间
    
    if (selectedDate < today) {
      return { isValid: false, message: '预约日期不能是过去日期' };
    }

    // 验证日期是否超过90天
    const maxDate = new Date();
    maxDate.setDate(maxDate.getDate() + 90);
    if (selectedDate > maxDate) {
      return { isValid: false, message: '预约日期不能超过90天' };
    }

    // 病人信息验证
    if (!patientInfo || !patientInfo.patientName || patientInfo.patientName.trim() === '') {
      return { isValid: false, message: '请输入病患姓名' };
    }
    if (!patientInfo.patientGenderValue || patientInfo.patientGenderValue.trim() === '') {
      return { isValid: false, message: '请选择病患性别' };
    }
    if (!patientInfo.patientIdCard || patientInfo.patientIdCard.trim() === '') {
      return { isValid: false, message: '请输入病患身份证号' };
    }
    if (!patientInfo.patientPhone || patientInfo.patientPhone.trim() === '') {
      return { isValid: false, message: '请输入病患手机号' };
    }
    if (!patientInfo.patientBirthday || patientInfo.patientBirthday.trim() === '') {
      return { isValid: false, message: '请选择病患生日' };
    }

    // 陪护人员验证
    if (!selectedStaffId) {
      return { isValid: false, message: '请选择陪护人员' };
    }

    // 陪护地点验证
    if (!locationInfo || !locationInfo.address || locationInfo.address.trim() === '') {
      return { isValid: false, message: '请输入陪护地点' };
    }
    if (locationInfo.address.trim().length < 5) {
      return { isValid: false, message: '陪护地点至少5个字符' };
    }
    if (locationInfo.address.trim().length > 200) {
      return { isValid: false, message: '陪护地点不能超过200个字符' };
    }

    // 房间号验证（可选，但有格式限制）
    if (locationInfo.room && locationInfo.room.trim() !== '') {
      if (locationInfo.room.trim().length > 20) {
        return { isValid: false, message: '房间号不能超过20个字符' };
      }
      // 房间号格式验证（数字+字母+特殊字符）
      const roomRegex = /^[0-9A-Za-z\-_\u4e00-\u9fa5]+$/;
      if (!roomRegex.test(locationInfo.room.trim())) {
        return { isValid: false, message: '房间号格式不正确' };
      }
    }

    // 特殊需求验证（可选，但有长度限制）
    if (this.data.demand && this.data.demand.trim().length > 500) {
      return { isValid: false, message: '特殊需求不能超过500个字符' };
    }

    // 协议同意验证
    if (!agree) {
      return { isValid: false, message: '请先同意服务协议' };
    }

    return { isValid: true, message: '验证通过' };
  }
}); 