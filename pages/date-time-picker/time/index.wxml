<view class="demo-desc">时分秒选择器</view>
<t-cell
  title="选择时间"
  hover
  note="{{secondText || ''}}"
  arrow
  data-mode="second"
  bindtap="showPicker"
  t-class="panel-item"
/>

<view class="demo-desc">时分选择器</view>
<t-cell
  title="选择时间"
  hover
  note="{{minuteText || ''}}"
  arrow
  data-mode="minute"
  bindtap="showPicker"
  t-class="panel-item"
/>

<!-- 时分 -->
<t-date-time-picker
  title="选择时间"
  visible="{{secondVisible}}"
  mode="{{['null', 'second']}}"
  value="{{second}}"
  format="HH:mm:ss"
  bindchange="onConfirm"
  bindpick="onColumnChange"
  bindcancel="hidePicker"
/>

<!-- 时分 -->
<t-date-time-picker
  title="选择时间"
  visible="{{minuteVisible}}"
  mode="{{['null', 'minute']}}"
  value="{{minute}}"
  format="HH:mm"
  bindchange="onConfirm"
  bindpick="onColumnChange"
  bindcancel="hidePicker"
/>
