/* pages/packages/detail.wxss */
.package-detail-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx; /* 为底部按钮留出空间 */
}

.top-margin {
  height: 88rpx; /* 与全局导航栏高度相等 */
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

.loading-text {
  color: #666;
  font-size: 28rpx;
}

/* 套餐主图 */
.package-hero {
  position: relative;
  width: 100%;
  height: 400rpx;
  overflow: hidden;
}

.hero-image {
  width: 100%;
  height: 100%;
}

.discount-badge {
  position: absolute;
  top: 24rpx;
  right: 24rpx;
  background: #FF6B35;
  color: #ffffff;
  font-size: 24rpx;
  font-weight: 600;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

/* 套餐基本信息 */
.package-basic-info {
  background: #ffffff;
  padding: 32rpx;
  margin-bottom: 16rpx;
}

.package-name {
  font-size: 36rpx;
  font-weight: 700;
  color: #1a1a1a;
  line-height: 1.4;
  margin-bottom: 16rpx;
}

.package-description {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 24rpx;
}

/* 富文本内容样式 */
.package-description rich-text {
  display: block;
}



/* 通用区域样式 */
.content-details-section {
  background: #ffffff;
  margin-bottom: 16rpx;
  padding: 32rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 24rpx;
}

/* 套餐详情内容 */
.content-details {
  line-height: 1.6;
}

.content-details rich-text {
  display: block;
}

/* 底部操作区域 */
.bottom-action {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #ffffff;
  border-top: 1rpx solid #f0f0f0;
  padding: 24rpx 32rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
}

.action-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 24rpx;
}

.price-summary {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.summary-price {
  font-size: 36rpx;
  font-weight: 700;
  color: #FF6B35;
  line-height: 1.2;
}

.summary-original {
  font-size: 24rpx;
  color: #999;
  text-decoration: line-through;
  margin-top: 4rpx;
}

.book-button {
  background: linear-gradient(135deg, #0C4147 0%, #1a5a61 100%);
  color: #ffffff;
  font-size: 32rpx;
  font-weight: 600;
  padding: 24rpx 48rpx;
  border-radius: 48rpx;
  text-align: center;
  transition: all 0.3s ease;
}

.book-button:active {
  transform: scale(0.95);
  opacity: 0.8;
}
