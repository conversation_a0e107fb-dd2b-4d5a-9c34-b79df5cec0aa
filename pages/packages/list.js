// pages/packages/list.js
import navbarBehavior from '../../behaviors/navbar';
const { getPackages } = require('../../api/packages.js');
const auth = require('../../utils/auth.js');

Page({
  behaviors: [navbarBehavior],
  
  data: {
    packages: [],
    loading: false,
    type: 'accompaniment', // 默认为预约陪诊
    title: '预约陪诊套餐',
    // 类型标题映射
    typeTitleMap: {
      'accompaniment': '预约陪诊套餐',
      'agency': '代办问诊套餐', 
      'hospitalization': '住院陪护套餐'
    }
  },

  onLoad(options) {
    console.log('套餐列表页面 onLoad, options:', options);
    
    // 检查登录状态
    const result = auth.checkLoginAndShowRegisterPrompt();
    if (!result) {
      console.log('登录检查失败，返回');
      return;
    }

    // 获取套餐类型
    const type = options.type || 'accompaniment';
    const title = this.data.typeTitleMap[type] || '套餐列表';
    
    this.setData({
      type,
      title
    });

    // 加载套餐列表
    this.loadPackages();
  },

  // 加载套餐列表
  loadPackages() {
    this.setData({ loading: true });
    
    getPackages({
      type: this.data.type,
      page: 1,
      limit: 20
    }).then(result => {
      console.log('获取套餐列表成功:', result);

      const packages = result.packages || [];

      // 处理套餐数据，添加默认图片和格式化价格
      const processedPackages = packages.map(pkg => ({
        ...pkg,
        // 添加默认图片
        image: pkg.image || '/assets/medical-5459653_960_720.webp',
        // 格式化价格 - 使用实际返回的price字段
        formattedPrice: this.formatPrice(pkg.price),
        // 保留原始HTML描述用于富文本显示
        richDescription: pkg.description || '',
        // 同时保留清理版本用于简短显示
        cleanDescription: this.cleanHtml(pkg.description || ''),

      }));

      this.setData({
        packages: processedPackages,
        loading: false
      });
    }).catch(err => {
      console.error('获取套餐列表失败:', err);
      this.setData({ loading: false });
      wx.showToast({
        title: '获取套餐列表失败',
        icon: 'none'
      });
    });
  },

  // 格式化价格
  formatPrice(price) {
    if (!price && price !== 0) return '0';
    return Number(price).toFixed(0);
  },

  // 清理HTML标签
  cleanHtml(html) {
    if (!html) return '';
    return html.replace(/<[^>]*>/g, '');
  },



  // 处理富文本内容，添加内联样式
  processRichText(html) {
    if (!html) return '';

    // 为常见HTML标签添加内联样式
    let processedHtml = html
      .replace(/<p>/g, '<p style="margin: 8rpx 0; line-height: 1.5; font-size: 26rpx; color: #666;">')
      .replace(/<h([1-6])>/g, '<h$1 style="font-weight: 600; color: #333; margin: 16rpx 0 8rpx 0; font-size: 30rpx;">')
      .replace(/<strong>/g, '<strong style="font-weight: 600; color: #333;">')
      .replace(/<b>/g, '<b style="font-weight: 600; color: #333;">')
      .replace(/<div>/g, '<div style="margin: 4rpx 0; line-height: 1.5; font-size: 26rpx; color: #666;">')
      .replace(/<span>/g, '<span style="font-size: 26rpx; color: #666;">');

    return processedHtml;
  },

  // 点击套餐卡片
  onPackageTap(e) {
    const packageId = e.currentTarget.dataset.id;
    if (!packageId) {
      wx.showToast({
        title: '套餐信息错误',
        icon: 'none'
      });
      return;
    }

    // 跳转到套餐详情页
    wx.navigateTo({
      url: `/pages/packages/detail?id=${packageId}&type=${this.data.type}`
    });
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadPackages();
    wx.stopPullDownRefresh();
  }
});
