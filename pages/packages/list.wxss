/* pages/packages/list.wxss */
.packages-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.top-margin {
  height: 88rpx; /* 与全局导航栏高度相等 */
}

.header-section {
  height: 120rpx;
  background: linear-gradient(135deg, #0C4147 0%, #1a5a61 100%);
}

.content-area {
  background-color: #f5f5f5;
  min-height: calc(100vh - 208rpx);
  padding: 32rpx 24rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

.loading-text {
  color: #666;
  font-size: 28rpx;
}

/* 空状态 */
.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

.empty-text {
  color: #999;
  font-size: 28rpx;
}

/* 套餐列表 */
.packages-list {
  display: flex;
  flex-direction: column;
}

/* 套餐卡片 */
.package-card {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  display: flex;
  align-items: flex-start;
  gap: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  margin-bottom: 20rpx;
}

.package-card:last-child {
  margin-bottom: 0;
}

.package-card:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.12);
}

/* 套餐图片 */
.package-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  overflow: hidden;
  flex-shrink: 0;
}

.package-image image {
  width: 100%;
  height: 100%;
}

/* 套餐信息 */
.package-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.package-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
  line-height: 1.4;
}



/* 价格容器 */
.price-container {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin: 8rpx 0;
}

.current-price {
  font-size: 36rpx;
  font-weight: 700;
  color: #FF6B35;
}

.original-price {
  font-size: 24rpx;
  color: #999;
  text-decoration: line-through;
}

.discount-tag {
  background: #FF6B35;
  color: #ffffff;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  font-weight: 500;
}

/* 特色标签 */
.features-container {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin: 8rpx 0;
}

.feature-tag {
  background: #E8F4F8;
  color: #0C4147;
  font-size: 22rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-weight: 500;
}

/* 服务时长 */
.duration-info {
  font-size: 24rpx;
  color: #666;
  margin-top: 4rpx;
}

.duration-label {
  color: #999;
}

.duration-value {
  color: #0C4147;
  font-weight: 500;
}

/* 箭头图标 */
.arrow-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 16rpx;
  height: 100%;
  align-self: center;
}
