<!-- 全局导航栏 -->
<global-navbar 
  title="{{title}}" 
  showBack="{{true}}"
  textColor="#ffffff"
  backgroundColor="#0C4147"
/>

<view class="packages-container">
  <!-- 顶部边距，与全局导航栏高度相等 -->
  <view class="top-margin"></view>
  
  <!-- 顶部深色背景区域 -->
  <view class="header-section">
    <!-- 保留深色背景，但不显示内容 -->
  </view>

  <!-- 主内容区域（浅色背景） -->
  <view class="content-area">
    <!-- 加载状态 -->
    <view wx:if="{{loading}}" class="loading-container">
      <view class="loading-text">加载中...</view>
    </view>

    <!-- 套餐列表 -->
    <view wx:else class="packages-list">
      <view wx:if="{{packages.length === 0}}" class="empty-container">
        <view class="empty-text">暂无套餐</view>
      </view>
      
      <view wx:else>
        <block wx:for="{{packages}}" wx:key="id">
          <view class="package-card" bindtap="onPackageTap" data-id="{{item.id}}">
            <!-- 套餐图片 -->
            <view class="package-image">
              <image src="{{item.image}}" mode="aspectFill" />
            </view>
            
            <!-- 套餐信息 -->
            <view class="package-info">
              <view class="package-name">{{item.name}}</view>

              <!-- 价格信息 -->
              <view class="price-container">
                <view class="current-price">¥{{item.formattedPrice}}</view>
              </view>
              
              <!-- 套餐特色 -->
              <view wx:if="{{item.features && item.features.length > 0}}" class="features-container">
                <block wx:for="{{item.features}}" wx:key="*this" wx:for-item="feature">
                  <view class="feature-tag">{{feature}}</view>
                </block>
              </view>
              
              <!-- 服务时长 -->
              <view wx:if="{{item.duration}}" class="duration-info">
                <text class="duration-label">服务时长：</text>
                <text class="duration-value">{{item.duration}}</text>
              </view>
            </view>
            
            <!-- 箭头图标 -->
            <view class="arrow-icon">
              <t-icon name="chevron-right" size="32rpx" color="#999" />
            </view>
          </view>
        </block>
      </view>
    </view>
  </view>
</view>
