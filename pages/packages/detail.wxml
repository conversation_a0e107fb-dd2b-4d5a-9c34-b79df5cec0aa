<!-- 全局导航栏 -->
<global-navbar 
  title="套餐详情" 
  showBack="{{true}}"
  textColor="#ffffff"
  backgroundColor="#0C4147"
/>

<view class="package-detail-container">
  <!-- 顶部边距，与全局导航栏高度相等 -->
  <view class="top-margin"></view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 套餐详情内容 -->
  <view wx:elif="{{packageInfo}}" class="detail-content">
    <!-- 套餐主图 -->
    <view class="package-hero">
      <image src="{{packageInfo.image}}" mode="aspectFill" class="hero-image" />
    </view>

    <!-- 套餐基本信息 -->
    <view class="package-basic-info">
      <view class="package-name">{{packageInfo.name}}</view>
      <view wx:if="{{packageInfo.richDescription}}" class="package-description">
        <rich-text nodes="{{packageInfo.richDescription}}" />
      </view>
    </view>

    <!-- 套餐详情 -->
    <view wx:if="{{packageInfo.richContentDetails}}" class="content-details-section">
      <view class="section-title">套餐详情</view>
      <view class="content-details">
        <rich-text nodes="{{packageInfo.richContentDetails}}" />
      </view>
    </view>
  </view>

  <!-- 底部预约按钮 -->
  <view wx:if="{{packageInfo}}" class="bottom-action">
    <view class="action-container">
      <view class="price-summary">
        <view class="summary-price">¥{{packageInfo.formattedPrice}}</view>
      </view>
      <view class="book-button" bindtap="onBookNow">立即预约</view>
    </view>
  </view>
</view>
