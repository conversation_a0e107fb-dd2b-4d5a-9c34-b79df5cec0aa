<wxs module="_"> module.exports.contain = function(arr, key) { return arr.indexOf(key) > -1 } </wxs>

<t-checkbox-group value="{{value}}" bind:change="onChange">
  <view wx:for="{{3}}" wx:key="index" class="card {{_.contain(value, index) ? 'card--active' : ''}}">
    <t-icon wx:if="{{_.contain(value, index)}}" name="check" t-class="card__icon" ariaHidden="{{true}}" />
    <t-checkbox
      value="{{index}}"
      label="多选"
      content="描述信息描述信息描述信息描述信息描述信息"
      icon="none"
      borderless
    />
  </view>
</t-checkbox-group>

<view class="demo-desc" style="margin: 48rpx 32rpx 32rpx">横向卡片多选框</view>

<t-checkbox-group t-class="horizontal-box" value="{{value1}}" bind:change="onChange1">
  <view wx:for="{{3}}" wx:key="index" class="card {{_.contain(value1, index) ? 'card--active' : ''}}">
    <t-icon wx:if="{{_.contain(value1, index)}}" name="check" t-class="card__icon" ariaHidden="{{true}}" />
    <t-checkbox value="{{index}}" label="多选" icon="none" borderless />
  </view>
</t-checkbox-group>
