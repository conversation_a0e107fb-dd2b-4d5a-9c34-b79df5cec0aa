.card {
  position: relative;
  margin: 32rpx;
  border-radius: 12rpx;
  overflow: hidden;
  box-sizing: border-box;
  border: 3rpx solid var(--td-bg-color-container, #fff);
}

.card--active {
  border-color: var(--td-brand-color, #0052d9);
}

.card--active::after {
  content: '';
  display: block;
  position: absolute;
  left: 0;
  top: 0;
  width: 0;
  height: 0;
  border-width: 28px 28px 28px 0;
  border-style: solid;
  border-color: #0052d9 transparent transparent transparent;
  border: 14px solid var(--td-brand-color, #0052d9);
  border-bottom-color: transparent;
  border-right-color: transparent;
}

.card__icon {
  color: var(--td-bg-color-container, #fff);
  position: absolute;
  left: 1.5px;
  top: 1.5px;
  z-index: 1;
}

/* 横向布局 */
.horizontal-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  margin: 32rpx;
}

.horizontal-box .card {
  flex: 0 0 calc(33.33% - 12rpx);
  margin: 0 0 24rpx 0;
}

.horizontal-box .card::after {
  border-width: 48rpx 48rpx 48rpx 0;
}

.horizontal-box .card__icon {
  font-size: 24rpx;
}
