<t-navbar class="demo-navbar" title="ColorPicker" leftArrow />

<view class="demo">
  <t-demo-header
    title="ColorPicker 颜色选择器"
    desc="用于颜色选择，支持多种格式。"
    notice="渲染框架支持情况：WebView"
  />
  <t-demo title="01 组件类型" desc="基础颜色选择器">
    <base />
  </t-demo>

  <t-demo desc="带色板的颜色选择器">
    <multiple />
  </t-demo>

  <t-demo desc="弹窗形式的颜色选择器" padding>
    <usePopup />
  </t-demo>

  <t-demo title="02 组件状态" desc="组件模式选择">
    <format />
  </t-demo>
</view>
