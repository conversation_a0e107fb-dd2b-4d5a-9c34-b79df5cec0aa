<wxs src="../calendar.wxs" module="_this" />

<t-cell
  arrow
  title="带单行描述的日历"
  note="{{_this.formatTimestamp(singleValue)}}"
  data-type="single"
  bind:tap="handleCalendar"
/>

<view class="demo-desc">带双行描述的日历</view>
<t-calendar
  visible="{{visible}}"
  value="{{type === 'single' ? singleValue : value}}"
  minDate="{{minDate}}"
  maxDate="{{maxDate}}"
  format="{{type === 'single' ? singleFormat: format}}"
  data-type="{{type}}"
  bind:confirm="handleConfirm"
/>
<t-cell
  arrow
  title="带双行描述的日历"
  note="{{_this.formatTimestamp(value)}}"
  data-type="multiple"
  bind:tap="handleCalendar"
/>
