<t-navbar title="Calendar" leftArrow />
<view class="demo">
  <t-demo-header title="Calendar 日历" desc="按照日历形式展示数据或日期的容器" notice="渲染框架支持情况：WebView" />
  <t-demo title="01 组件类型" desc="基础日历">
    <base />
  </t-demo>

  <t-demo desc="">
    <multiple />
  </t-demo>

  <t-demo desc="带单行描述的日历">
    <custom-text />
  </t-demo>

  <t-demo desc="带翻页功能的日历">
    <switch-mode />
  </t-demo>

  <t-demo desc="可选择区间日期的日历">
    <range />
  </t-demo>

  <t-demo title="02 组件样式" desc="国际化">
    <local-text />
  </t-demo>

  <t-demo desc="含不可选的日历">
    <custom-range />
  </t-demo>

  <t-demo desc="不使用 Popup">
    <without-popup />
  </t-demo>
</view>
