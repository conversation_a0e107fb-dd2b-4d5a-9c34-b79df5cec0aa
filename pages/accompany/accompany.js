import navbarBehavior from '../../behaviors/navbar';
const auth = require('../../utils/auth.js');
const { getHospitalList } = require('../../utils/hospital.js');
const { API_BASE } = require('../../api/base.js');

Page({
  behaviors: [navbarBehavior],
  data: {
    staffList: [], // 初始化为空
    selectedStaffId: null,
    selectedHospitalId: null, // 新增字段
    selectedHospitalIndex: 0, // 新增
    hospitalList: [], // 新增
    date: '',
    time: '',
    demand: '',
    showSuccess: false,
    newOrderId: null, // 新增字段
    // 病患信息（从组件获取）
    patientInfo: {
      patientId: null,
      patientName: '',
      patientGender: '',
      patientGenderValue: '',
      patientIdCard: '',
      patientPhone: '',
      patientBirthday: ''
    },
    locationInfo: { address: '' },
    // 时间限制
    minDate: '',
    maxTime: '18:00',
    // 套餐相关
    packageId: null,
    packageType: null,
    packageInfo: null,
  },
  
  onLoad(options) {
    console.log('accompany 页面 onLoad, options:', options);
    console.log('[accompany] 开始检查登录状态');
    // 检查登录状态并显示注册提示
    const result = auth.checkLoginAndShowRegisterPrompt();
    console.log('[accompany] 登录检查结果:', result);
    if (!result) {
      console.log('[accompany] 登录检查失败，返回');
      return;
    }
    console.log('[accompany] 登录检查通过，继续执行');

    // 获取套餐ID和类型
    const packageId = options.packageId;
    const type = options.type;
    if (packageId) {
      console.log('[accompany] 套餐预约模式，套餐ID:', packageId, '类型:', type);
      this.setData({ packageId, packageType: type });
      // 如果有套餐ID，可以加载套餐详情来获取价格等信息
      this.loadPackageInfo(packageId);
    }

    // 设置最小日期为今天
    this.setMinDate();
    // 请求陪诊师列表
    console.log('准备请求陪诊师列表');
    this.loadHospitals();
  },

  // 设置最小日期为今天
  setMinDate() {
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    const minDate = `${year}-${month}-${day}`;
    
    this.setData({ minDate });
  },

  // 验证选择的时间是否有效
  validateDateTime() {
    const { date, time, minDate } = this.data;
    
    if (!date || !time) {
      return { valid: false, message: '请选择日期和时间' };
    }

    // 检查日期是否为今天
    if (date === minDate) {
      // 如果是今天，检查时间是否已过
      const now = new Date();
      const currentHour = now.getHours();
      const currentMinute = now.getMinutes();
      
      const [selectedHour, selectedMinute] = time.split(':').map(Number);
      
      if (selectedHour < currentHour || (selectedHour === currentHour && selectedMinute <= currentMinute)) {
        return { valid: false, message: '预约时间不能早于当前时间' };
      }
    }

    // 检查时间是否在合理范围内（8:00-18:00）
    const [selectedHour] = time.split(':').map(Number);
    if (selectedHour < 8 || selectedHour > 18) {
      return { valid: false, message: '预约时间应在8:00-18:00之间' };
    }

    // 检查日期是否超过30天
    const selectedDate = new Date(date);
    const today = new Date();
    const diffTime = selectedDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays > 30) {
      return { valid: false, message: '预约时间不能超过30天' };
    }

    return { valid: true };
  },

  // 加载套餐信息
  loadPackageInfo(packageId) {
    const { getPackageDetail } = require('../../api/packages.js');
    getPackageDetail(packageId).then(packageInfo => {
      console.log('[accompany] 获取套餐详情成功:', packageInfo);
      this.setData({ packageInfo });
    }).catch(err => {
      console.error('[accompany] 获取套餐详情失败:', err);
      wx.showToast({
        title: '获取套餐信息失败',
        icon: 'none'
      });
    });
  },

  async loadHospitals() {
    const hospitals = await getHospitalList();
    this.setData({
      hospitals,
      hospitalIndex: 0,
      selectedHospitalId: hospitals.length > 0 ? hospitals[0].id : null
    }, () => {
      if (hospitals.length > 0) this.loadStaffByHospital(hospitals[0].id);
    });
  },
  loadStaffByHospital(hospitalId) {
    auth.requestWithToken({
      url: `${API_BASE}/medical/medical_staff`,
      method: 'GET',
      data: { type: 'companion', hospitalId, page: 1, limit: 1000 },
      success: (res) => {
        if (res.data && res.data.success && res.data.data && Array.isArray(res.data.data.medicalStaff)) {
          this.setData({ staffList: res.data.data.medicalStaff, selectedStaffId: null });
        } else {
          wx.showToast({ title: '获取陪诊师失败', icon: 'none' });
        }
      },
      fail: () => wx.showToast({ title: '网络错误', icon: 'none' })
    });
  },
  onHospitalChange(e) {
    const hospitalIndex = Number(e.detail.value);
    const hospitalId = this.data.hospitals[hospitalIndex].id;
    this.setData({ hospitalIndex, selectedHospitalId: hospitalId, selectedStaffId: null });
    this.loadStaffByHospital(hospitalId);
  },

  onStaffSelect(e) {
    const staffId = e.currentTarget.dataset.id;
    const staff = this.data.staffList.find(item => item.id === staffId);
    const hospitalId = staff && staff.hospital && staff.hospital.id ? staff.hospital.id : null;
    this.setData({ selectedStaffId: staffId, selectedHospitalId: hospitalId });
  },
  onDateChange(e) {
    const selectedDate = e.detail.value;
    this.setData({ date: selectedDate });
    
    // 如果选择的日期是今天，需要验证时间
    if (selectedDate === this.data.minDate && this.data.time) {
      const validation = this.validateDateTime();
      if (!validation.valid) {
        wx.showToast({ title: validation.message, icon: 'none' });
        this.setData({ time: '' }); // 清空时间
      }
    }
  },
  onTimeChange(e) {
    const selectedTime = e.detail.value;
    this.setData({ time: selectedTime });
    
    // 验证时间是否有效
    const validation = this.validateDateTime();
    if (!validation.valid) {
      wx.showToast({ title: validation.message, icon: 'none' });
      this.setData({ time: '' }); // 清空时间
    }
  },
  onDemandInput(e) {
    const demand = e.detail.value;
    this.setData({ demand: demand });
    
    // 实时验证需求说明长度
    if (demand && demand.trim().length > 500) {
      wx.showToast({ title: '需求说明不能超过500个字符', icon: 'none' });
    }
  },
  onLocationAddressInput(e) {
    const address = e.detail.value;
    this.setData({ locationInfo: { ...this.data.locationInfo, address: address } });
    
    // 实时验证地址长度
    if (address && address.trim().length > 200) {
      wx.showToast({ title: '地址不能超过200个字符', icon: 'none' });
    }
  },
  // 病患信息变化事件处理
  onPatientChange(e) {
    const patientInfo = e.detail;
    this.setData({ patientInfo });
  },
  // onChooseLocation() {
  //   // 先检查位置权限
  //   wx.getSetting({
  //     success: (res) => {
  //       if (res.authSetting['scope.userLocation'] === false) {
  //         // 用户之前拒绝了位置权限，引导用户开启
  //         wx.showModal({
  //           title: '需要位置权限',
  //           content: '选择陪诊地点需要获取您的位置信息，请在设置中开启位置权限',
  //           confirmText: '去设置',
  //           success: (modalRes) => {
  //             if (modalRes.confirm) {
  //               wx.openSetting();
  //             }
  //           }
  //         });
  //         return;
  //       }
        
  //       // 调用地图选点
  //       wx.chooseLocation({
  //         success: (res) => {
  //           console.log('选点成功:', res);
  //           this.setData({
  //             locationInfo: { ...this.data.locationInfo, address: res.address + (res.name ? '（' + res.name + '）' : '') }
  //           });
  //           wx.showToast({ title: '地点选择成功', icon: 'success' });
  //         },
  //         fail: (err) => {
  //           console.error('选点失败:', err);
  //           if (err.errMsg && err.errMsg.includes('cancel')) {
  //             // 用户取消，不提示
  //             return;
  //           }
  //           wx.showToast({ title: '选点失败，请重试', icon: 'none' });
  //         }
  //       });
  //     }
  //   });
  // },
  onReserve() {
    // 基本表单校验
    const validationResult = this.validateForm();
    if (!validationResult.isValid) {
      wx.showToast({ title: validationResult.message, icon: 'none' });
      return;
    }
    
    // 获取价格：优先使用套餐价格，否则使用陪诊师价格
    let price = 100; // 默认价格
    if (this.data.packageInfo && this.data.packageInfo.price) {
      price = Number(this.data.packageInfo.price);
      console.log('[accompany] 使用套餐价格:', price);
    } else {
      const staff = this.data.staffList.find(item => item.id === this.data.selectedStaffId);
      price = staff && staff.servicePrice && staff.servicePrice.standard ? staff.servicePrice.standard : 100;
      console.log('[accompany] 使用陪诊师价格:', price);
    }
    const userInfo = wx.getStorageSync('userInfo') || {};
    // 关键：以 picker 选中的医院为准
    const hospitalId = this.data.hospitals && this.data.hospitals.length > 0
      ? this.data.hospitals[this.data.hospitalIndex].id
      : null;
    if (!hospitalId || hospitalId <= 0) {
      wx.showToast({ title: '请选择医院', icon: 'none' });
      return;
    }
    // 构建病患信息
    const patientInfo = this.data.patientInfo;
    const patientId = patientInfo.patientId || null;
    const patientName = patientInfo.patientName || '';
    const patientGender = patientInfo.patientGender || '';
    const patientGenderValue = patientInfo.patientGenderValue || ''; // 用于提交的英文性别值
    const patientIdCard = patientInfo.patientIdCard || '';
    const patientPhone = patientInfo.patientPhone || '';
    const patientBirthday = patientInfo.patientBirthday || '';
    
    // 根据是否有patientId来决定传递方式
    let payload = {
      serviceType: 'companion',
      status: 'pending',
      price,
      remarks: this.data.demand || '',
      userId: userInfo.id,
      staffId: this.data.selectedStaffId,
      hospitalId, // 用 picker 选中的医院
      locationInfo: this.data.locationInfo || { address: '' },
      attachments: this.data.attachments || [],
      appointmentTime: this.data.date && this.data.time ? `${this.data.date}T${this.data.time}:00.000Z` : '',
      serviceDetails: {
        items: [],
        requirements: this.data.demand || ''
      },
      // 添加套餐信息
      packageId: this.data.packageId || null,
      packageType: this.data.packageType || null
    };
    
    if (patientId) {
      // 如果有预设病患ID，传递patientId和patientInfo
      payload.patientId = patientId;
      payload.patientInfo = {
        name: patientName,
        gender: patientGenderValue, // 使用英文性别值
        idCard: patientIdCard,
        phone: patientPhone,
        birthday: patientBirthday
      };
    } else {
      // 如果没有预设病患ID，只传递patientInfo
      payload.patientInfo = {
        name: patientName,
        gender: patientGenderValue, // 使用英文性别值
        idCard: patientIdCard,
        phone: patientPhone,
        birthday: patientBirthday
      };
    }
    
    // 调试信息
    console.log('提交的payload:', payload);
    console.log('病患信息:', payload.patientInfo);
    const auth = require('../../utils/auth.js');
    auth.requestWithToken({
      url: `${API_BASE}/orders/service_orders`, // 已验证的API
      method: 'POST',
      data: payload,
      success: (res) => {
        console.log('预约陪诊返回', res);
        if (res.data && res.data.success) {
          // 新增：弹窗引导去支付
          this.setData({
            showSuccess: true,
            newOrderId: res.data.data && res.data.data.id // 假设返回新订单id
          });
        } else {
          const msg = (res.data && (res.data.message || (res.data.errors && res.data.errors[0] && res.data.errors[0].msg))) || '预约失败';
          wx.showToast({ title: msg, icon: 'none', duration: 2000 });
        }
      },
      fail: (err) => {
        console.error('预约陪诊请求失败', err);
        wx.showToast({ title: '网络错误', icon: 'none' });
      }
    });
  },
  onCloseSuccess() {
    this.setData({ showSuccess: false });
  },
  onGoToOrderDetail() {
    // 跳转到订单页面
    this.setData({ showSuccess: false });
    // 添加短暂延迟，确保订单数据已保存
    wx.showLoading({ title: '跳转中...' });
    setTimeout(() => {
      wx.hideLoading();
      wx.switchTab({ url: '/pages/orders/orders' });
    }, 500);
  },

  // 表单验证函数
  validateForm() {
    const {
      hospitals,
      hospitalIndex,
      selectedStaffId,
      date,
      time,
      patientInfo,
      locationInfo
    } = this.data;

    // 医院验证
    if (!hospitals || hospitals.length === 0) {
      return { isValid: false, message: '暂无可用医院' };
    }
    if (hospitalIndex < 0 || hospitalIndex >= hospitals.length) {
      return { isValid: false, message: '请选择医院' };
    }

    // 陪诊师验证
    if (!selectedStaffId) {
      return { isValid: false, message: '请选择陪诊师' };
    }

    // 日期验证
    if (!date || date.trim() === '') {
      return { isValid: false, message: '请选择预约日期' };
    }

    // 时间验证
    if (!time || time.trim() === '') {
      return { isValid: false, message: '请选择预约时间' };
    }

    // 验证日期时间组合是否有效
    const timeValidation = this.validateDateTime();
    if (!timeValidation.valid) {
      return { isValid: false, message: timeValidation.message };
    }

    // 病患信息验证
    if (!patientInfo.patientName || patientInfo.patientName.trim() === '') {
      return { isValid: false, message: '请输入病患姓名' };
    }
    if (!patientInfo.patientGenderValue || patientInfo.patientGenderValue.trim() === '') {
      return { isValid: false, message: '请选择病患性别' };
    }
    if (!patientInfo.patientIdCard || patientInfo.patientIdCard.trim() === '') {
      return { isValid: false, message: '请输入病患身份证号' };
    }
    if (!patientInfo.patientPhone || patientInfo.patientPhone.trim() === '') {
      return { isValid: false, message: '请输入病患手机号' };
    }
    if (!patientInfo.patientBirthday || patientInfo.patientBirthday.trim() === '') {
      return { isValid: false, message: '请选择病患生日' };
    }
    
    // 验证身份证号格式
    const idCardRegex = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
    if (!idCardRegex.test(patientInfo.patientIdCard)) {
      return { isValid: false, message: '身份证号格式不正确' };
    }
    
    // 验证手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(patientInfo.patientPhone)) {
      return { isValid: false, message: '手机号格式不正确' };
    }

    // 陪诊地点验证
    if (!locationInfo || !locationInfo.address || locationInfo.address.trim() === '') {
      return { isValid: false, message: '请输入陪诊地点' };
    }
    if (locationInfo.address.trim().length < 5) {
      return { isValid: false, message: '陪诊地点至少5个字符' };
    }
    if (locationInfo.address.trim().length > 200) {
      return { isValid: false, message: '陪诊地点不能超过200个字符' };
    }

    // 需求说明验证（可选，但有长度限制）
    if (this.data.demand && this.data.demand.trim().length > 500) {
      return { isValid: false, message: '需求说明不能超过500个字符' };
    }

    return { isValid: true, message: '验证通过' };
  }
}); 