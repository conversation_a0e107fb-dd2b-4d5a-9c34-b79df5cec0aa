<!-- 全局导航栏 -->
<global-navbar 
  title="陪诊服务" 
  showBack="{{true}}"
  textColor="#ffffff"
  backgroundColor="#0C4147"
/>

<view class="accompany-m3-container">
  <!-- 顶部边距，与全局导航栏高度相等 -->
  <view class="m3-top-margin"></view>
  
  <!-- 顶部深色背景区域 -->
  <view class="m3-header-section">
    <!-- 保留深色背景，但不显示内容 -->
  </view>

  <!-- 主内容区域（浅色背景） -->
  <view class="m3-content-area">
    <!-- 医院选择卡片 -->
    <view class="m3-card">
      <view class="m3-card-title">
        <t-icon name="home" size="24rpx" color="#0C4147" />
        <text>选择医院</text>
        <text class="required">*</text>
      </view>
      <picker mode="selector" range="{{hospitals}}" range-key="name" value="{{hospitalIndex}}" bindchange="onHospitalChange">
        <view class="m3-picker">{{hospitals.length ? hospitals[hospitalIndex].name : '请选择医院'}}</view>
      </picker>
    </view>

    <!-- 陪诊师选择卡片 -->
    <view class="m3-card">
      <view class="m3-card-title">
        <t-icon name="user-business" size="24rpx" color="#0C4147" />
        <text>选择陪诊师</text>
        <text class="required">*</text>
      </view>
      <view class="m3-staff-list">
        <block wx:for="{{staffList}}" wx:key="id">
          <view class="m3-staff-card {{selectedStaffId === item.id ? 'm3-active' : ''}}" bindtap="onStaffSelect" data-id="{{item.id}}">
            <view class="m3-staff-avatar">
              <t-icon name="user-business" size="32rpx" color="#0C4147" />
            </view>
            <view class="m3-staff-info">
              <view class="m3-staff-name">{{item.user.username}}</view>
              <view class="m3-staff-rating">评分：{{item.serviceScore}} | 价格：¥{{item.servicePrice.standard}}</view>
              <view class="m3-staff-intro" wx:if="{{item.introduction}}">{{item.introduction}}</view>
            </view>
          </view>
        </block>
      </view>
    </view>

    <!-- 预约时间卡片 -->
    <view class="m3-card">
      <view class="m3-card-title">
        <t-icon name="calendar" size="24rpx" color="#0C4147" />
        <text>预约时间</text>
        <text class="required">*</text>
      </view>
      <view class="m3-time-picker">
        <picker mode="date" value="{{date}}" start="{{minDate}}" bindchange="onDateChange">
          <view class="m3-picker">{{date || '选择日期'}}</view>
        </picker>
        <picker mode="time" value="{{time}}" start="08:00" end="18:00" bindchange="onTimeChange">
          <view class="m3-picker">{{time || '选择时间'}}</view>
        </picker>
      </view>
    </view>

    <!-- 病患信息组件 -->
    <patient-selector 
      showSelector="{{true}}" 
      required="{{true}}"
      bind:patientChange="onPatientChange"
    />

    <!-- 陪诊地点卡片 -->
    <view class="m3-card">
      <view class="m3-card-title">
        <t-icon name="location" size="24rpx" color="#0C4147" />
        <text>陪诊地点</text>
        <text class="required">*</text>
      </view>
      <view class="m3-location-input">
        <input class="m3-input" placeholder="请输入详细地址" value="{{locationInfo.address}}" bindinput="onLocationAddressInput"/>
        <!-- <button class="m3-map-btn" bindtap="onChooseLocation">地图选点</button> -->
      </view>
    </view>

    <!-- 需求说明卡片 -->
    <view class="m3-card">
      <view class="m3-card-title">
        <t-icon name="edit" size="24rpx" color="#0C4147" />
        <text>需求说明</text>
      </view>
      <textarea class="m3-textarea" placeholder="请填写具体需求（可选）" value="{{demand}}" bindinput="onDemandInput" />
    </view>

    <!-- 预约按钮 -->
    <view class="m3-reserve-btn" bindtap="onReserve">预约陪诊</view>
  </view>
</view>

<!-- 成功弹窗 -->
<view wx:if="{{showSuccess}}" class="m3-success-modal">
  <view class="m3-modal-content">
    <view class="m3-modal-title">预约成功</view>
    <view class="m3-modal-desc">请尽快联系工作人员完成线下支付</view>
    <view class="m3-modal-btn" bindtap="onGoToOrderDetail">查看订单</view>
    <view class="m3-modal-tip">* 需完成支付，订单才会生效</view>
  </view>
</view> 