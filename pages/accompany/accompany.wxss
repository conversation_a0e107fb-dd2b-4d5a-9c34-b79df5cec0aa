/* 顶部边距，与全局导航栏高度相等 */
.m3-top-margin {
  height: 120rpx;
  padding-top: env(safe-area-inset-top);
}

/* 顶部深色背景区域 */
.m3-header-section {
  background: #0C4147;
  padding: 40rpx 24rpx 40rpx 24rpx;
  position: relative;
  z-index: 2;
}

/* 主内容区域（浅色背景） */
.m3-content-area {
  background: #F5F5F5;
  min-height: 100vh;
  margin-top: -20rpx;
  padding: 40rpx 48rpx 32rpx 48rpx;
  z-index: 1;
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 32rpx;
  border-radius: 32rpx 32rpx 0 0;
}

/* 容器背景 */
.accompany-m3-container {
  background: #F5F5F5;
  min-height: 100vh;
}

/* 卡片基础样式 */
.m3-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px 0 rgba(12, 65, 71, 0.08);
  padding: 32rpx 24rpx;
  margin-bottom: 0;
}

.m3-card-title {
  font-size: 24rpx;
  color: #0C4147;
  font-weight: 700;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.m3-card-title text {
  font-size: 24rpx;
  color: #0C4147;
  font-weight: 700;
}

.required {
  color: #BA1A1A;
  font-weight: 700;
}

/* 选择器样式 */
.m3-picker {
  background: #F5F5F5;
  border-radius: 8px;
  padding: 24rpx;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
  transition: all 0.3s ease;
  border: 1px solid rgba(12, 65, 71, 0.1);
  min-height: 88rpx;
  display: flex;
  align-items: center;
  line-height: 1.4;
}

.m3-picker:active {
  background: #E8E8E8;
  border-color: #0C4147;
}

/* 时间选择器布局 */
.m3-time-picker {
  display: flex;
  gap: 16rpx;
}

.m3-time-picker .m3-picker {
  flex: 1;
}

/* 陪诊师列表 */
.m3-staff-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.m3-staff-card {
  display: flex;
  align-items: flex-start;
  padding: 24rpx;
  border-radius: 8px;
  background: #F5F5F5;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.m3-staff-card.m3-active {
  background: #CDEDA3;
  border-color: #4C662B;
  box-shadow: 0 2px 8px 0 rgba(76, 102, 43, 0.15);
}

.m3-staff-avatar {
  margin-right: 20rpx;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48rpx;
  height: 48rpx;
}

.m3-staff-info {
  flex: 1;
}

.m3-staff-name {
  font-size: 28rpx;
  font-weight: 700;
  color: #4C662B;
  margin-bottom: 8rpx;
}

.m3-staff-mobile {
  font-size: 22rpx;
  color: #586249;
  margin-left: 8rpx;
  font-weight: 400;
}

.m3-staff-hospital {
  font-size: 24rpx;
  color: #586249;
  margin-bottom: 8rpx;
}

.m3-staff-intro {
  font-size: 22rpx;
  color: #586249;
  margin-bottom: 8rpx;
  line-height: 1.4;
}

.m3-staff-qual {
  font-size: 22rpx;
  color: #586249;
  margin-bottom: 8rpx;
}

.m3-staff-rating {
  font-size: 22rpx;
  color: #586249;
  margin-bottom: 8rpx;
}

.m3-staff-days {
  font-size: 22rpx;
  color: #586249;
}



/* 输入框样式 */
.m3-input {
  background: #F5F5F5;
  border-radius: 8px;
  padding: 24rpx;
  font-size: 28rpx;
  color: #333;
  border: 1px solid rgba(12, 65, 71, 0.1);
  outline: none;
  width: 100%;
  box-sizing: border-box;
  min-height: 88rpx;
  transition: all 0.3s ease;
}

.m3-input:focus {
  border-color: #0C4147;
  background: #fff;
}

.m3-input::placeholder {
  color: #999;
  font-size: 28rpx;
  line-height: 1.4;
}

.m3-location-input {
  display: flex;
  gap: 16rpx;
  align-items: center;
}

.m3-map-btn {
  background: #4C662B;
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 16rpx 24rpx;
  font-size: 24rpx;
  white-space: nowrap;
}

/* 文本域样式 */
.m3-textarea {
  background: #F5F5F5;
  border-radius: 8px;
  padding: 24rpx;
  font-size: 28rpx;
  color: #333;
  border: 1px solid rgba(12, 65, 71, 0.1);
  outline: none;
  width: 100%;
  min-height: 120rpx;
  box-sizing: border-box;
  resize: none;
  transition: all 0.3s ease;
  line-height: 1.4;
}

.m3-textarea:focus {
  border-color: #0C4147;
  background: #fff;
}

.m3-textarea::placeholder {
  color: #999;
  font-size: 28rpx;
  line-height: 1.4;
}

/* 预约按钮 */
.m3-reserve-btn {
  background: #0C4147;
  color: #fff;
  border-radius: 12px;
  padding: 24rpx;
  text-align: center;
  font-size: 32rpx;
  font-weight: 700;
  margin-top: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(12, 65, 71, 0.2);
  transition: all 0.2s;
}

.m3-reserve-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(12, 65, 71, 0.3);
}

/* 成功弹窗 */
.m3-success-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.m3-modal-content {
  background: #fff;
  border-radius: 16rpx;
  padding: 48rpx 32rpx;
  margin: 48rpx;
  text-align: center;
  max-width: 600rpx;
  width: 100%;
}

.m3-modal-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #0C4147;
  margin-bottom: 16rpx;
}

.m3-modal-desc {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 32rpx;
  line-height: 1.4;
}

.m3-modal-btn {
  background: #0C4147;
  color: #fff;
  border-radius: 8px;
  padding: 20rpx 32rpx;
  font-size: 28rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
}

.m3-modal-tip {
  font-size: 22rpx;
  color: #999;
  font-style: italic;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .accompany-container {
    padding: 16rpx;
    gap: 16rpx;
  }
  
  .m3-card {
    padding: 24rpx 16rpx;
  }
  
  .m3-staff-card {
    padding: 16rpx;
  }
  
  .m3-time-picker {
    flex-direction: column;
    gap: 12rpx;
  }
  
  .m3-location-input {
    flex-direction: column;
    align-items: stretch;
  }
  
  .m3-map-btn {
    width: 100%;
  }
} 