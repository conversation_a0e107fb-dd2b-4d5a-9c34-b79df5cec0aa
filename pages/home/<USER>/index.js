// 服务入口配置
const list = [
  {
    name: '预约陪诊',
    icon: 'user-business',
    iconType: 'tdesign',
    path: '/pages/packages/list?type=accompaniment',
  },
  {
    name: '代办服务',
    icon: 'service',
    iconType: 'tdesign',
    path: '/pages/proxy-service/proxy-service',
  },
  {
    name: '代办问诊',
    icon: 'user-business',
    iconType: 'tdesign',
    path: '/pages/packages/list?type=agency',
  },
  {
    name: '代办买药',
    icon: 'hospital',
    iconType: 'tdesign',
    path: '/pages/medicine-order/medicine-order',
  },
  {
    name: '住院陪护',
    icon: 'home',
    iconType: 'tdesign',
    path: '/pages/packages/list?type=hospitalization',
  },
  {
    name: '诊前挂号',
    icon: 'calendar',
    iconType: 'tdesign',
    path: '/pages/register/register',
  },
  {
    name: '服务流程',
    icon: 'list',
    iconType: 'tdesign',
    path: '/pages/service-flow/service-flow',
  },
  {
    name: '更多服务',
    icon: 'more',
    iconType: 'tdesign',
    path: 'services', // 特殊标识，用于区分跳转方式
  },
];
const skylineList = list;
export { list, skylineList };
