/* 顶部边距，与全局导航栏高度相等 */
.home-m3-container{
  padding-bottom:60rpx;
}
.m3-top-margin {
  height: 120rpx;
  padding-top: env(safe-area-inset-top);
}

/* 顶部深色背景区域 */
.m3-header-section {
  background: #0C4147;
  padding: 40rpx 24rpx 40rpx 24rpx;
  position: relative;
  z-index: 2;
}

.m3-header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
}

.m3-header-title {
  color: #fff;
  font-size: 36rpx;
  font-weight: 700;
  letter-spacing: 0.2px;
}

.m3-header-actions {
  display: flex;
  align-items: center;
  gap: 24rpx;
}



/* 主内容区域（浅色背景） */
.m3-content-area {
  background: #F5F5F5;
  min-height: 100vh;
  margin-top: -20rpx;
  padding: 40rpx 48rpx 32rpx 48rpx;
  z-index: 1;
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  border-radius: 32rpx 32rpx 0 0;
}

/* 轮播图区域样式 */
.m3-swiper-section {
  margin: 0 0 24rpx 0;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  /* 添加硬件加速和稳定的变换 */
  transform: translateZ(0);
  will-change: transform;
  /* 确保轮播图容器稳定 */
  position: relative;
  z-index: 1;
}

/* 医院入口卡片样式 */
.m3-hospital-entry-card {
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  padding: 32rpx 24rpx;
  transition: all 0.2s ease;
  cursor: pointer;
}

.m3-hospital-entry-card:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.12);
}

.m3-hospital-entry-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.m3-hospital-entry-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.m3-hospital-entry-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #F0F9F8;
  border-radius: 12rpx;
  width: 80rpx;
  height: 80rpx;
  margin-right: 24rpx;
}

.m3-hospital-entry-info {
  flex: 1;
}

.m3-hospital-entry-title {
  font-size: 32rpx;
  color: #0C4147;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.m3-hospital-entry-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

.m3-hospital-entry-right {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 双入口卡片区域样式 */
.m3-dual-entry-section {
  display: flex;
  gap: 24rpx;
  margin-bottom: 24rpx;
}

.m3-entry-card {
  flex: 1;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  padding: 24rpx 20rpx;
  transition: all 0.2s ease;
  cursor: pointer;
}

.m3-entry-card:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.12);
}

.m3-ranking-card {
  border-left: 4rpx solid #FF6B35;
}

.m3-hospital-card {
  border-left: 4rpx solid #68C4C1;
}

.m3-entry-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.m3-entry-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.m3-entry-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12rpx;
  width: 64rpx;
  height: 64rpx;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.m3-ranking-card .m3-entry-icon {
  background: #FFF5F2;
}

.m3-hospital-card .m3-entry-icon {
  background: #F0F9F8;
}

.m3-entry-info {
  flex: 1;
  min-width: 0;
}

.m3-entry-title {
  font-size: 28rpx;
  color: #0C4147;
  font-weight: 600;
  margin-bottom: 4rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.m3-entry-desc {
  font-size: 22rpx;
  color: #666;
  line-height: 1.3;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.m3-entry-right {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

/* 陪诊日记列表样式 */
.m3-diary-section {
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  padding: 32rpx 24rpx;
}

.m3-diary-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.m3-diary-title {
  font-size: 32rpx;
  color: #0C4147;
  font-weight: 600;
}

.m3-diary-more {
  font-size: 24rpx;
  color: #666;
  cursor: pointer;
}

.m3-diary-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.m3-diary-item {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 24rpx;
  transition: all 0.2s ease;
  cursor: pointer;
}

.m3-diary-item:active {
  transform: scale(0.98);
  background: #f0f0f0;
}

.m3-diary-content {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.m3-diary-text {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.m3-diary-title-text {
  font-size: 28rpx;
  color: #0C4147;
  font-weight: 500;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.m3-diary-date {
  font-size: 22rpx;
  color: #999;
}

.m3-diary-image {
  width: 120rpx;
  height: 80rpx;
  border-radius: 8rpx;
  overflow: hidden;
  flex-shrink: 0;
}

.m3-diary-image image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.m3-diary-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
}

.m3-loading-text {
  font-size: 24rpx;
  color: #999;
}

.m3-diary-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
}

.m3-empty-text {
  font-size: 24rpx;
  color: #999;
}

/* 卡片 */
.m3-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px 0 rgba(12, 65, 71, 0.08);
  padding: 24rpx 20rpx;
}

.m3-card-title {
  font-size: 24rpx;
  color: #0C4147;
  font-weight: 700;
  margin-bottom: 12rpx;
}

/* 旧的搜索区域样式 - 注释掉 */
/*
.m3-search-section {
  padding: 0 24rpx;
  margin-top: 24rpx;
}
*/

/* 搜索卡片 */
.m3-search-card {
  padding: 24rpx;
}

/* 服务网格 */
.m3-service-card-block {
  min-height: auto;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.m3-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
}
.m3-service-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px 0 rgba(12, 65, 71, 0.08);
  padding: 32rpx 0;
  transition: box-shadow 0.2s;
}

/* 服务项目容器 */
.m3-service-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

/* 服务入口圆形按钮 */
.m3-service-entry {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #F5F5F5;
  border-radius: 50%;
  width: 110rpx;
  height: 110rpx;
  padding: 0;
  margin-bottom: 12rpx;
  transition: all 0.2s ease;
  position: relative;
}
.m3-service-entry:active {
  background: #E8E8E8;
  transform: scale(0.95);
}

.m3-chip-entry {
  background: #F5F5F5;
  color: #0C4147;
  border-radius: 8px;
  padding: 16rpx 0;
  font-size: 24rpx;
  font-weight: 600;
  text-align: center;
  position: relative;
  transition: background 0.2s, color 0.2s;
  flex: 1;
  margin-right: 8rpx;
}
.m3-chip-entry:last-child {
  margin-right: 0;
}
.m3-chip-entry:active {
  background: #E8E8E8;
}

/* 服务图标样式 */
.m3-service-icon {
  font-size: 48rpx;
  margin-bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #68C4C1;
}

/* 服务名称样式 */
.m3-service-name {
  font-size: 20rpx;
  color: #0C4147;
  font-weight: 500;
  text-align: center;
  margin-top: 0;
  line-height: 1.2;
}

/* 快捷入口 Segmented/Chip */
.m3-segmented-card {
  padding-bottom: 0;
}
.m3-segmented {
  display: flex;
  gap: 16rpx;
}
.m3-chip {
  flex: 1;
  background: #0C4147;
  color: #fff;
  border-radius: 8px;
  padding: 16rpx 0;
  font-size: 24rpx;
  font-weight: 600;
  text-align: center;
  position: relative;
  transition: background 0.2s, color 0.2s;
}
.m3-chip:active {
  background: #0A353A;
}
.m3-badge {
  position: absolute;
  top: 2rpx;
  right: 16rpx;
  background: #BA1A1A;
  color: #fff;
  font-size: 18rpx;
  padding: 2rpx 8rpx;
  border-radius: 12rpx;
  min-width: 24rpx;
  text-align: center;
  font-weight: 600;
}



/* 导航栏头像样式 */
.navbar-avatar {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: #fff;
  border: 2rpx solid #0C4147;
}
