<view class="address-container">
  <!-- 页面头部 -->
  <view class="page-header">
    <view class="header-bg"></view>
    <view class="header-content">
      <view class="header-title">📍 地址管理</view>
      <view class="header-subtitle">管理您的收货地址</view>
    </view>
  </view>

  <!-- 地址统计 -->
  <view class="stats-section">
    <view class="stats-card">
      <view class="stats-icon">📍</view>
      <view class="stats-info">
        <view class="stats-number">{{addressList.length}}</view>
        <view class="stats-label">地址数量</view>
      </view>
    </view>
    <view class="stats-card">
      <view class="stats-icon">🏠</view>
      <view class="stats-info">
        <view class="stats-number">{{defaultCount}}</view>
        <view class="stats-label">默认地址</view>
      </view>
    </view>
    <view class="stats-card">
      <view class="stats-icon">🚚</view>
      <view class="stats-info">
        <view class="stats-number">快速</view>
        <view class="stats-label">配送服务</view>
      </view>
    </view>
  </view>

  <!-- 地址列表 -->
  <view class="address-section">
    <view class="section-header">
      <view class="section-title">我的地址</view>
      <view class="section-desc">管理您的收货地址信息</view>
    </view>
    
    <block wx:for="{{addressList}}" wx:key="id">
      <view class="address-card">
        <view class="card-header">
          <view class="address-icon">
            <view wx:if="{{item.type === 'home'}}" class="icon-home">🏠</view>
            <view wx:elif="{{item.type === 'work'}}" class="icon-work">🏢</view>
            <view wx:elif="{{item.type === 'hospital'}}" class="icon-hospital">🏥</view>
            <view wx:else class="icon-default">📍</view>
          </view>
          <view class="address-info">
            <view class="address-name">{{item.name}}</view>
            <view class="address-type">{{item.typeLabel}}</view>
          </view>
          <view class="address-status">
            <view wx:if="{{item.isDefault}}" class="default-badge">默认</view>
          </view>
        </view>
        
        <view class="card-content">
          <view class="address-detail">
            <view class="detail-icon">📍</view>
            <view class="detail-text">{{item.detail}}</view>
          </view>
          
          <view class="contact-info">
            <view class="contact-item">
              <view class="contact-icon">📞</view>
              <view class="contact-text">{{item.phone}}</view>
            </view>
            <view class="contact-item" wx:if="{{item.remark}}">
              <view class="contact-icon">📝</view>
              <view class="contact-text">{{item.remark}}</view>
            </view>
          </view>
        </view>
        
        <view class="card-footer">
          <view class="action-btn edit-btn">
            <view class="btn-icon">✏️</view>
            <view class="btn-text">编辑</view>
          </view>
          <view class="action-btn delete-btn">
            <view class="btn-icon">🗑️</view>
            <view class="btn-text">删除</view>
          </view>
          <view wx:if="{{!item.isDefault}}" class="action-btn default-btn">
            <view class="btn-icon">⭐</view>
            <view class="btn-text">设为默认</view>
          </view>
        </view>
      </view>
    </block>
    
    <!-- 空状态 -->
    <view wx:if="{{addressList.length === 0}}" class="empty-state">
      <view class="empty-icon">📍</view>
      <view class="empty-title">暂无地址</view>
      <view class="empty-desc">添加收货地址以便快速下单</view>
    </view>
  </view>

  <!-- 添加按钮 -->
  <view class="add-btn" bindtap="onAdd">
    <view class="add-icon">+</view>
    <view class="add-text">新增地址</view>
  </view>
</view> 