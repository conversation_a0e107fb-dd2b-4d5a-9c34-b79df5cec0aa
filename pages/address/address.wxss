/* 地址页面容器 */
.address-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* 页面头部 */
.page-header {
  position: relative;
  padding: 0;
  overflow: hidden;
}

.header-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 240rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 0 0 40rpx 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.3);
}

.header-content {
  position: relative;
  z-index: 2;
  padding: 88rpx 32rpx 48rpx 32rpx;
  text-align: center;
}

.header-title {
  font-size: 40rpx;
  font-weight: 700;
  color: #fff;
  margin-bottom: 12rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.header-subtitle {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 统计区域 */
.stats-section {
  padding: 0 32rpx;
  margin-top: -20rpx;
  position: relative;
  z-index: 1;
  display: flex;
  gap: 16rpx;
}

.stats-card {
  flex: 1;
  background: #fff;
  border-radius: 20rpx;
  padding: 24rpx;
  text-align: center;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
}

.stats-icon {
  font-size: 40rpx;
  margin-bottom: 8rpx;
}

.stats-number {
  font-size: 32rpx;
  font-weight: 700;
  color: #333;
}

.stats-label {
  font-size: 24rpx;
  color: #666;
}

/* 地址区域 */
.address-section {
  padding: 32rpx;
  margin-top: 32rpx;
}

.section-header {
  margin-bottom: 32rpx;
  text-align: center;
}

.section-title {
  font-size: 32rpx;
  font-weight: 700;
  color: #333;
  margin-bottom: 8rpx;
}

.section-desc {
  font-size: 26rpx;
  color: #666;
}

/* 地址卡片 */
.address-card {
  background: #fff;
  border-radius: 24rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
}

.address-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
}

.address-card:active {
  transform: translateY(-4rpx);
  box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.12);
}

/* 卡片头部 */
.card-header {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.address-icon {
  margin-right: 24rpx;
}

.address-icon > view {
  width: 80rpx;
  height: 80rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
}

.icon-home {
  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
}

.icon-work {
  background: linear-gradient(135deg, #1677ff 0%, #4096ff 100%);
}

.icon-hospital {
  background: linear-gradient(135deg, #f5222d 0%, #ff4d4f 100%);
}

.icon-default {
  background: linear-gradient(135deg, #faad14 0%, #ffc53d 100%);
}

.address-info {
  flex: 1;
}

.address-name {
  font-size: 32rpx;
  font-weight: 700;
  color: #333;
  margin-bottom: 8rpx;
}

.address-type {
  font-size: 24rpx;
  color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  display: inline-block;
  font-weight: 500;
}

.address-status {
  display: flex;
  align-items: center;
}

.default-badge {
  background: linear-gradient(135deg, #faad14 0%, #ffc53d 100%);
  color: #fff;
  font-size: 22rpx;
  padding: 6rpx 16rpx;
  border-radius: 16rpx;
  font-weight: 600;
}

/* 卡片内容 */
.card-content {
  padding: 24rpx 32rpx;
}

.address-detail {
  display: flex;
  align-items: flex-start;
  gap: 12rpx;
  margin-bottom: 20rpx;
  background: rgba(102, 126, 234, 0.05);
  padding: 16rpx;
  border-radius: 16rpx;
}

.detail-icon {
  font-size: 24rpx;
  margin-top: 4rpx;
}

.detail-text {
  flex: 1;
  font-size: 26rpx;
  color: #333;
  line-height: 1.5;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.contact-icon {
  font-size: 24rpx;
  width: 24rpx;
  text-align: center;
}

.contact-text {
  font-size: 26rpx;
  color: #666;
}

/* 卡片底部 */
.card-footer {
  padding: 0 32rpx 32rpx 32rpx;
  display: flex;
  gap: 12rpx;
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  padding: 16rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
  font-weight: 600;
  transition: all 0.3s ease;
}

.edit-btn {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
}

.edit-btn:active {
  background: rgba(102, 126, 234, 0.2);
  transform: scale(0.95);
}

.delete-btn {
  background: rgba(245, 34, 45, 0.1);
  color: #f5222d;
}

.delete-btn:active {
  background: rgba(245, 34, 45, 0.2);
  transform: scale(0.95);
}

.default-btn {
  background: rgba(250, 173, 20, 0.1);
  color: #faad14;
}

.default-btn:active {
  background: rgba(250, 173, 20, 0.2);
  transform: scale(0.95);
}

.btn-icon {
  font-size: 24rpx;
}

.btn-text {
  font-size: 24rpx;
}

/* 添加按钮 */
.add-btn {
  position: fixed;
  right: 48rpx;
  bottom: 120rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  border-radius: 50rpx;
  padding: 24rpx 32rpx;
  display: flex;
  align-items: center;
  gap: 12rpx;
  box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.4);
  transition: all 0.3s ease;
  z-index: 100;
}

.add-btn:active {
  transform: scale(0.95);
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.5);
}

.add-icon {
  font-size: 32rpx;
  font-weight: 700;
}

.add-text {
  font-size: 28rpx;
  font-weight: 600;
}

/* 空状态 */
.empty-state {
  padding: 120rpx 32rpx;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
  opacity: 0.6;
}

.empty-title {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: 600;
}

.empty-desc {
  font-size: 26rpx;
  color: #999;
  line-height: 1.5;
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.address-card {
  animation: fadeInUp 0.6s ease-out;
}

.address-card:nth-child(2) {
  animation-delay: 0.1s;
}

.address-card:nth-child(3) {
  animation-delay: 0.2s;
}

.address-card:nth-child(4) {
  animation-delay: 0.3s;
}

.address-card:nth-child(5) {
  animation-delay: 0.4s;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .header-content {
    padding: 88rpx 24rpx 40rpx 24rpx;
  }
  
  .stats-section {
    padding: 0 24rpx;
    gap: 12rpx;
  }
  
  .stats-card {
    padding: 20rpx;
  }
  
  .address-section {
    padding: 24rpx;
  }
  
  .card-header {
    padding: 24rpx;
  }
  
  .card-content {
    padding: 20rpx 24rpx;
  }
  
  .card-footer {
    padding: 0 24rpx 24rpx 24rpx;
    flex-direction: column;
    gap: 12rpx;
  }
  
  .action-btn {
    padding: 12rpx;
  }
  
  .add-btn {
    right: 32rpx;
    bottom: 100rpx;
    padding: 20rpx 28rpx;
  }
} 