# 订单页设计文档

## 1. 页面目标

- 支持多种订单类型（服务、挂号、药品、其他）
- 支持按订单状态筛选
- 展示订单核心信息、当前状态、可用操作
- 支持跳转订单详情页
- 适配不同订单类型的状态流转

---

## 2. 页面结构

### 2.1 顶部Tab

- 一级Tab：订单类型
    - 服务订单（service）
    - 挂号订单（registration）
    - 药品订单（medicine）
    - 其他（other）
- 二级Tab：订单状态（根据类型动态显示）
    - 全部
    - 各类型特有状态（见下表）

### 2.2 订单列表

- 卡片/列表形式展示
- 每条订单显示：
    - 订单编号
    - 订单类型
    - 订单状态（映射为中文）
    - 主要内容（如服务名称、医院、药品、时间等）
    - 金额
    - 下单时间
    - 可用操作按钮（如去支付、取消、退款、确认完成等）

### 2.3 空状态/加载/分页

- 无订单时友好提示
- 支持下拉刷新、分页加载

---

## 3. 订单类型与状态映射

| 订单类型     | 状态值（status）                    | 中文显示         | 可用操作（示例）         |
|--------------|-------------------------------------|------------------|--------------------------|
| 服务订单     | pending                             | 待处理           | 去支付、取消             |
|              | paid                                | 已支付           | 取消、申请退款           |
|              | assigned                            | 已分配           | 取消、申请退款           |
|              | in_progress                         | 进行中           | 取消                     |
|              | completed                           | 已完成           | 申请售后                 |
|              | cancelled                           | 已取消           | -                        |
|              | refunded                            | 已退款           | -                        |
| 挂号订单     | pending                             | 待处理           | 取消                     |
|              | registered                          | 已挂号           | 取消                     |
|              | completed                           | 已完成           | -                        |
|              | cancelled                           | 已取消           | -                        |
| 药品订单     | pending                             | 待处理           | 去支付、取消             |
|              | paid                                | 已支付           | 取消、申请退款           |
|              | delivered                           | 已发货           | 确认收货、申请退款       |
|              | completed                           | 已完成           | 申请售后                 |
|              | cancelled                           | 已取消           | -                        |
|              | refunded                            | 已退款           | -                        |

---

## 4. 状态筛选Tab建议

- 服务订单：全部、待处理、已支付、已分配、进行中、已完成、已取消、已退款
- 挂号订单：全部、待处理、已挂号、已完成、已取消
- 药品订单：全部、待处理、已支付、已发货、已完成、已取消、已退款

---

## 5. 订单列表字段建议

| 字段         | 说明                   | 备注/来源           |
|--------------|------------------------|---------------------|
| orderNo      | 订单编号               | serviceOrder/medicineOrder/registrationOrder等 |
| type         | 订单类型               | service/medicine/registration/other |
| status       | 订单状态               | 需映射为中文        |
| mainInfo     | 主要内容（如服务/药品/医院） | 取决于订单类型      |
| amount       | 金额                   | price/totalAmount等 |
| createdAt    | 下单时间               |                     |
| actions      | 可用操作按钮           | 动态生成            |

---

## 6. 交互说明

- 切换类型/状态自动刷新列表
- 点击订单卡片跳转详情页
- 操作按钮根据状态和类型动态显示
- 支持下拉刷新、分页加载

---

## 7. 状态映射代码建议（JS）

```js
const statusMap = {
  service: {
    pending: '待处理',
    paid: '已支付',
    assigned: '已分配',
    in_progress: '进行中',
    completed: '已完成',
    cancelled: '已取消',
    refunded: '已退款'
  },
  registration: {
    pending: '待处理',
    registered: '已挂号',
    completed: '已完成',
    cancelled: '已取消'
  },
  medicine: {
    pending: '待处理',
    paid: '已支付',
    delivered: '已发货',
    completed: '已完成',
    cancelled: '已取消',
    refunded: '已退款'
  }
};
```

---

## 8. 其它建议

- 订单详情页应展示所有状态流转历史
- 后端返回的 status 字段应与本文档保持一致
- 如有新订单类型/状态，需同步更新本文档和前端映射

---

## 9. 订单详情页操作按钮设计

### 9.1 总体原则
- 按钮只在合理状态下显示，避免无意义操作。
- staff（医护/后台）与病患（普通用户）显示的操作不同，staff 主要处理订单流转，病患主要支付、取消、确认等。
- 不同订单类型有不同的操作流转，需分别设计。

### 9.2 服务订单（如陪诊、住院、代办等）

#### 病患端
| 订单状态         | 可用操作按钮         | 说明                         |
|------------------|---------------------|------------------------------|
| pending（待处理）| 取消订单、去支付     | 取消/支付                    |
| paid（已支付）   | 取消订单、申请退款   | 支付后可取消/退款            |
| assigned（已分配）| 取消订单、申请退款  | 仍可取消/退款                |
| in_progress（进行中）| 取消订单         | 服务已开始，仅可取消         |
| completed（已完成）| 申请售后           | 售后入口                     |
| cancelled/ refunded| 无                 | 不显示操作                   |

#### staff端
| 订单状态         | 可用操作按钮         | 说明                         |
|------------------|---------------------|------------------------------|
| paid（已支付）   | 分配服务人员         | 进入分配流程                 |
| assigned（已分配）| 开始服务            | 标记为 in_progress           |
| in_progress（进行中）| 完成服务          | 标记为 completed             |
| 其他             | 无                  |                              |

### 9.3 药品订单

#### 病患端
| 订单状态         | 可用操作按钮         | 说明                         |
|------------------|---------------------|------------------------------|
| pending（待处理）| 取消订单、去支付     |                              |
| paid（已支付）   | 取消订单、申请退款   |                              |
| delivered（已发货）| 确认收货、申请退款 |                              |
| completed（已完成）| 申请售后           |                              |
| cancelled/ refunded| 无                 |                              |

#### staff端
| 订单状态         | 可用操作按钮         | 说明                         |
|------------------|---------------------|------------------------------|
| paid（已支付）   | 发货                | 标记为 delivered             |
| delivered（已发货）| 无操作             | 不显示完成订单按钮           |
| 其他             | 无                  |                              |

### 9.4 挂号订单

#### 病患端
| 订单状态         | 可用操作按钮         | 说明                         |
|------------------|---------------------|------------------------------|
| pending（待处理）| 取消订单            |                              |
| registered（已挂号）| 取消订单          |                              |
| completed（已完成）| 无                 |                              |
| cancelled        | 无                  |                              |

#### staff端
| 订单状态         | 可用操作按钮         | 说明                         |
|------------------|---------------------|------------------------------|
| pending（待处理）| 确认挂号            | 标记为 registered            |
| registered（已挂号）| 完成服务          | 标记为 completed（使用服务订单的完成按钮）|
| 其他             | 无                  |                              |

### 9.4.1 挂号订单特殊说明

**重要：挂号订单不显示"完成挂号"按钮**
- 挂号订单的完成通过服务订单的"完成服务"按钮统一处理
- 当服务订单状态变为 `completed` 时，挂号订单自动变为 `completed`
- 避免重复操作和状态不一致问题

**挂号单号显示：**
- 挂号单号（`registrationNo`）应在基本信息卡片中显示
- 挂号单号来自挂号订单表，通过关联字段获取
- 如果挂号单号为空，则不显示该字段

### 9.5 按钮显示逻辑伪代码（JS）

```js
function getOrderActions({ type, status, userRole }) {
  if (userRole === 'patient') {
    if (type === 'service') {
      if (status === 'pending') return ['取消订单', '去支付'];
      if (status === 'paid' || status === 'assigned') return ['取消订单', '申请退款'];
      if (status === 'in_progress') return ['取消订单'];
      if (status === 'completed') return ['申请售后'];
    }
    if (type === 'medicine') {
      if (status === 'pending') return ['取消订单', '去支付'];
      if (status === 'paid') return ['取消订单', '申请退款'];
      if (status === 'delivered') return ['确认收货', '申请退款'];
      if (status === 'completed') return ['申请售后'];
    }
    if (type === 'registration') {
      if (status === 'pending' || status === 'registered') return ['取消订单'];
    }
  }
  if (userRole === 'staff') {
    if (type === 'service') {
      if (status === 'paid') return ['分配服务人员'];
      if (status === 'assigned') return ['开始服务'];
      if (status === 'in_progress') return ['完成服务'];
    }
    if (type === 'medicine') {
      if (status === 'paid') return ['发货'];
      if (status === 'delivered') return []; // 移除完成订单按钮
    }
    if (type === 'registration') {
      if (status === 'pending') return ['确认挂号'];
      // 移除完成挂号按钮，统一使用服务订单的完成服务按钮
    }
  }
  return [];
}
```

### 9.6 交互说明
- 所有操作需二次确认弹窗，如“确定要取消订单吗？”
- 操作后自动刷新订单详情，并返回上一页或提示成功。
- 按钮禁用/隐藏：已取消、已退款、已完成等终态不显示操作按钮。

### 9.7 UI建议
- 按钮区建议底部悬浮，主操作高亮（如“去支付”、“确认收货”）。
- 多个按钮时主次分明，避免误操作。
- staff端可用操作按钮可用不同颜色区分（如“分配服务人员”为蓝色，“完成服务”为绿色）。

### 9.x 药品订单（medicine/proxy）在服务订单详情页的特殊处理

> ⚠️ 特殊说明：
> - 买药服务订单（proxy）下只会有一个药品子订单（medicineOrders[0]）。
> - 订单详情页的药品相关操作按钮（如“发货”“完成订单”）均以 medicineOrders[0] 的 status 字段为准动态显示。
> - 只有当父服务订单（即本页订单）状态为 in_progress 时，才允许操作药品子订单，否则所有药品相关按钮隐藏。
> - 按钮事件直接操作 medicineOrders[0] 的状态（如发货、完成等），不会影响主服务订单的其他状态。
> - 这样保证药品订单流转与主服务订单严格联动，避免越权或误操作。
>
> // 此特殊处理已同步前端实现，后续如有变更需同步更新 