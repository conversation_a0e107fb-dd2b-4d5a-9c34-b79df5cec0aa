# 药品订单状态显示测试说明

## 测试目标

验证药品订单（`serviceType: 'proxy'`）不显示原有的订单状态部分，只显示专用的药品状态流转组件。

## 测试场景

### 1. 药品订单页面
**预期结果**：
- ✅ 不显示原有的订单状态流转（灰色圆圈和连接线）
- ✅ 显示专用的药品状态流转组件
- ✅ 同时显示"服务状态"和"配送状态"两个区块
- ✅ 支持状态步骤点击交互

**测试步骤**：
1. 进入药品订单详情页
2. 检查订单状态区块
3. 确认只显示新的药品状态组件
4. 点击状态步骤验证交互功能

### 2. 非药品订单页面
**预期结果**：
- ✅ 显示原有的订单状态流转
- ✅ 不显示药品状态流转组件
- ✅ 保持原有交互功能

**测试步骤**：
1. 进入陪诊、挂号等其他类型订单详情页
2. 检查订单状态区块
3. 确认显示原有的状态流转
4. 验证原有功能正常

## 关键修改点

### 1. WXML结构修改
```xml
<!-- 药品订单专用状态流转 -->
<view class="m3-card status-flow-card" wx:if="{{order.serviceType === 'proxy'}}">
  <medicine-status-flow />
</view>

<!-- 其他订单原有状态流转 -->
<view class="m3-card status-flow-card" wx:if="{{order.serviceType !== 'proxy'}}">
  <view class="m3-status-flow">
    <!-- 原有状态流转内容 -->
  </view>
</view>
```

### 2. JavaScript数据优化
```javascript
// 仅非药品订单生成原有状态流转数据
const statusFlow = order.serviceType !== 'proxy' ? this.generateStatusFlow(order) : [];
```

## 验证要点

1. **条件渲染正确性**
   - 药品订单：只显示新组件
   - 其他订单：只显示原有组件

2. **数据加载优化**
   - 药品订单不生成冗余的状态流转数据
   - 组件按需加载，提升性能

3. **交互功能完整性**
   - 新组件的状态步骤点击功能正常
   - 原有订单的交互功能不受影响

4. **样式一致性**
   - 新组件样式与页面整体风格一致
   - 卡片布局和间距保持统一

## 常见问题排查

### 问题1：药品订单仍显示原有状态流转
**可能原因**：
- 条件判断逻辑错误
- 数据更新不及时

**解决方案**：
- 检查 `order.serviceType` 值是否为 `'proxy'`
- 确认条件渲染语法正确

### 问题2：新组件不显示
**可能原因**：
- 组件路径错误
- 组件注册失败

**解决方案**：
- 检查组件路径和注册
- 查看控制台错误信息

### 问题3：交互功能异常
**可能原因**：
- 事件绑定错误
- 数据传递问题

**解决方案**：
- 检查事件处理函数
- 验证数据传递正确性

## 测试用例

| 订单类型 | 预期显示 | 状态流转 | 交互功能 |
|----------|----------|----------|----------|
| 药品订单 | 新组件 | 双重状态 | 支持点击 |
| 陪诊订单 | 原有组件 | 单一状态 | 原有功能 |
| 挂号订单 | 原有组件 | 单一状态 | 原有功能 |
| 住院订单 | 原有组件 | 单一状态 | 原有功能 |

## 总结

通过条件渲染和数据优化，确保药品订单完全使用新的状态流转组件，避免显示原有的订单状态部分，同时保持其他类型订单的原有功能不变。 