# 小程序审核前临时修改说明

## 修改目的
为了快速通过小程序审核，暂时移除用户自定义发布内容功能，避免审核不通过。

## 修改内容

### 1. 健康日记页面 (pages/diary/list)
- **文件**: `pages/diary/list.wxml` 和 `pages/diary/list.js`
- **修改内容**:
  - 注释掉新建日记按钮（第81-85行）
  - 将tab列表从 `['我的', '广场']` 改为 `['广场']`
  - 修改 `loadDiariesByTab` 方法，让tabIndex 0对应广场数据
  - 修改 `filterDiariesByPermission` 方法，只显示公开日记

### 2. 修改详情
- **新建按钮**: 已注释，用户无法创建新日记
- **Tab切换**: 只显示"广场"tab，隐藏"我的"tab
- **数据过滤**: 只显示privacyLevel为'public'的日记
- **权限控制**: 用户只能查看公开日记，无法查看个人日记

## 恢复方法
审核通过后，需要：
1. 取消注释新建按钮代码
2. 恢复tabList为 `['我的', '广场']`
3. 恢复 `loadDiariesByTab` 方法的原始逻辑
4. 恢复 `filterDiariesByPermission` 方法的原始逻辑

## 注意事项
- 所有修改都添加了注释说明，便于后续恢复
- 保留了原始代码作为注释，确保恢复时不会出错
- 修改后的功能仍然可以正常查看公开日记内容 