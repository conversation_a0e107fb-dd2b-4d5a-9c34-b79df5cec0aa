# 订单API文档

本文档记录不同订单类型的实际提交字段和返回响应，用于指导订单详情页的信息显示。

## 订单类型列表

### 1. 陪诊服务 (companion)

#### 表单项核对

**✅ 表单中的字段：**
1. **医院选择** → `hospitalId`
2. **陪诊师选择** → `staffId`
3. **预约时间** → `date` + `time` → 合并为 `appointmentTime`
4. **病人选择** → `patientId`, `patientName`, `patientGender`, `patientIdCard`, `patientPhone`, `patientBirthday`
5. **陪诊地点** → `locationInfo.address`
6. **需求说明** → `demand` → 映射到 `remarks` 和 `serviceDetails.requirements`

**❌ 表单中没有的字段：**
- 房间号 (已移除)
- 陪诊时长 (已移除)

#### 实际提交字段 (POST /orders/service_orders)
```json
{
  "serviceType": "companion",
  "status": "pending",
  "price": 100,
  "remarks": "具体需求如下",
  "userId": 6,
  "staffId": 2,
  "hospitalId": 2,
  "patientId": 3,
  "patientName": "病人1",
  "patientGender": "男",
  "patientIdCard": "177777188809122222",
  "patientPhone": "18700000001",
  "patientBirthday": "2014-07-28",
  "locationInfo": {
    "address": "陕西省西安市未央区凤城八路风景御园第19幢1单元1层10108,10109（中国邮政储蓄银行(西安凤城八路支行征信查询点)）"
  },
  "attachments": [],
  "appointmentTime": "2025-07-30T08:00:00.000Z",
  "serviceDetails": {
    "items": [],
    "requirements": "具体需求如下"
  }
}
```

#### 字段映射关系

| 表单字段 | 提交字段 | 说明 |
|----------|----------|------|
| `hospitals[hospitalIndex].id` | `hospitalId` | 医院ID |
| `selectedStaffId` | `staffId` | 陪诊师ID |
| `date` + `time` | `appointmentTime` | 合并为ISO格式时间 |
| `selectedPatient.patient_id` | `patientId` | 患者ID |
| `selectedPatient.name` | `patientName` | 患者姓名 |
| `patientGender` (转换后) | `patientGender` | 患者性别(中文) |
| `selectedPatient.id_card` | `patientIdCard` | 患者身份证 |
| `selectedPatient.phone` | `patientPhone` | 患者手机号 |
| `selectedPatient.birthday` | `patientBirthday` | 患者生日 |
| `locationInfo.address` | `locationInfo.address` | 陪诊地点 |
| `demand` | `remarks` | 需求说明 |
| `demand` | `serviceDetails.requirements` | 需求说明(重复) |
| `staff.servicePrice.standard` | `price` | 价格(从陪诊师信息获取) |
| `userInfo.id` | `userId` | 用户ID(从本地存储获取) |

#### 字段分析

**✅ 实际传递的字段：**
- `serviceType`: 服务类型 (companion)
- `status`: 订单状态 (pending)
- `price`: 价格 (从陪诊师信息自动获取)
- `remarks`: 备注 (来自需求说明)
- `userId`: 用户ID (从本地存储获取)
- `staffId`: 陪诊师ID (用户选择)
- `hospitalId`: 医院ID (用户选择)
- `patientId`: 患者ID (用户选择)
- `patientName`: 患者姓名 (从患者信息获取)
- `patientGender`: 患者性别 (中文转换后)
- `patientIdCard`: 患者身份证 (从患者信息获取)
- `patientPhone`: 患者手机号 (从患者信息获取)
- `patientBirthday`: 患者生日 (从患者信息获取)
- `locationInfo.address`: 陪诊地点地址 (用户输入或地图选点)
- `attachments`: 附件 (空数组，表单中没有此功能)
- `appointmentTime`: 预约时间 (日期+时间合并)
- `serviceDetails.requirements`: 需求说明 (与remarks重复)

**❌ 未传递的字段：**
- `locationInfo.room`: 房间号 (陪诊服务不需要，表单已移除)
- `duration`: 陪诊时长 (陪诊服务不需要，表单已移除)
- `patientInfo`: 患者信息对象 (直接使用独立字段)

**🔄 自动生成的字段：**
- `price`: 从陪诊师信息中获取
- `userId`: 从本地存储的用户信息获取
- `appointmentTime`: 由日期和时间字段合并生成
- `serviceDetails.items`: 空数组(陪诊服务不需要具体项目)

#### 返回响应
```json
{
  "success": true,
  "data": {
    "id": 8,
    "orderNo": "SRV2025072817030570S5P5",
    "userId": 6,
    "staffId": 2,
    "hospitalId": 2,
    "serviceType": "companion",
    "serviceDetails": {
      "items": [],
      "requirements": "具体需求如下"
    },
    "status": "pending",
    "appointmentTime": "2025-07-30T08:00:00.000Z",
    "duration": null,
    "price": "100.00",
    "paymentInfo": null,
    "patientInfo": {
      "name": "病人1",
      "gender": "male",
      "idCard": "177777188809122222",
      "phone": "18700000001",
      "birthday": "2014-07-28"
    },
    "patientId": "3",
    "locationInfo": {
      "address": "陕西省西安市未央区凤城八路风景御园第19幢1单元1层10108,10109（中国邮政储蓄银行(西安凤城八路支行征信查询点)）"
    },
    "rating": null,
    "review": null,
    "attachments": [],
    "completedAt": null,
    "createdBy": 6,
    "updatedBy": null,
    "createdAt": "2025-07-28T09:03:05.984Z",
    "updatedAt": "2025-07-28T09:03:05.984Z",
    "deletedAt": null,
    "user": {
      "id": 6,
      "username": "wx_QW9UdY",
      "mobile": "19800000010"
    },
    "staff": {
      "id": 2,
      "type": "companion",
      "serviceScore": "5.0"
    },
    "hospital": {
      "id": 2,
      "name": "测试医院1",
      "level": "三级甲等",
      "address": {
        "city": "西安",
        "detail": "雁塔区丈八四路",
        "province": "陕西"
      }
    },
    "creator": {
      "id": 6,
      "username": "wx_QW9UdY"
    }
  },
  "message": "服务订单创建成功"
}
```

#### 订单详情页显示字段分析

**✅ 需要显示的信息：**

**基本信息：**
- 订单号：`orderNo`
- 订单状态：`status`
- 创建时间：`createdAt`
- 价格：`price`

**服务信息：**
- 服务类型：`serviceType` (显示为"陪诊服务")
- 预约时间：`appointmentTime`
- 需求说明：`serviceDetails.requirements`

**患者信息：**
- 姓名：`patientInfo.name`
- 性别：`patientInfo.gender` (需要转换为中文显示)
- 身份证：`patientInfo.idCard`
- 手机号：`patientInfo.phone`
- 生日：`patientInfo.birthday`

**陪诊信息：**
- 陪诊地点：`locationInfo.address`
- 陪诊师：`staff` 相关信息
- 医院：`hospital` 相关信息

**用户信息：**
- 用户名：`user.username`
- 手机号：`user.mobile`

**❌ 不需要显示的信息：**
- 房间号：`locationInfo.room` (陪诊服务没有此字段)
- 陪诊时长：`duration` (陪诊服务没有此字段)

---

### 2. 住院陪护 (hospitalize)

#### 表单项核对

**✅ 表单中的字段：**
1. **医院选择** → `hospitalId`
2. **陪护天数** → `days` → 映射到 `duration`
3. **预约日期** → `date` → 映射到 `appointmentTime`
4. **病人选择** → `patientId`, `patientName`, `patientGender`, `patientIdCard`, `patientPhone`, `patientBirthday`
5. **陪护地点** → `locationInfo.address`
6. **房间号** → `locationInfo.room`
7. **特殊需求** → `demand` → 映射到 `remarks` 和 `serviceDetails.requirements`
8. **陪护人员选择** → `staffId`
9. **协议同意** → `agree` (前端验证)

#### 实际提交字段 (POST /orders/service_orders)
```json
{
  "serviceType": "hospitalization",
  "status": "pending",
  "price": 200,
  "remarks": "好好照顾",
  "userId": 6,
  "staffId": 2,
  "hospitalId": 2,
  "patientId": 3,
  "patientName": "病人1",
  "patientGender": "男",
  "patientIdCard": "177777188809122222",
  "patientPhone": "18700000001",
  "patientBirthday": "2014-07-28",
  "locationInfo": {
    "address": "陕西省西安市未央区张家堡街道凤城八路158号风景御园18栋1单元10102室（岚嵉叙传统茶(凤城八路店)）",
    "room": "101"
  },
  "attachments": null,
  "appointmentTime": "2025-07-29T00:00:00.000Z",
  "duration": 2,
  "serviceDetails": {
    "items": [
      {
        "desc": "好好照顾",
        "type": "hospitalization",
        "staffId": 2
      }
    ],
    "requirements": "好好照顾"
  }
}
```

#### 字段映射关系

| 表单字段 | 提交字段 | 说明 |
|----------|----------|------|
| `hospitals[hospitalIndex].id` | `hospitalId` | 医院ID |
| `selectedStaffId` | `staffId` | 陪护师ID |
| `date` | `appointmentTime` | 转换为ISO格式时间 |
| `days` | `duration` | 陪护天数 |
| `selectedPatient.patient_id` | `patientId` | 患者ID |
| `selectedPatient.name` | `patientName` | 患者姓名 |
| `patientGender` (转换后) | `patientGender` | 患者性别(中文) |
| `selectedPatient.id_card` | `patientIdCard` | 患者身份证 |
| `selectedPatient.phone` | `patientPhone` | 患者手机号 |
| `selectedPatient.birthday` | `patientBirthday` | 患者生日 |
| `locationInfo.address` | `locationInfo.address` | 陪护地点 |
| `locationInfo.room` | `locationInfo.room` | 房间号 |
| `demand` | `remarks` | 特殊需求 |
| `demand` | `serviceDetails.requirements` | 特殊需求(重复) |
| `demand` | `serviceDetails.items[0].desc` | 特殊需求(项目描述) |
| `staff.servicePrice.standard * days` | `price` | 价格(陪护师价格 × 天数) |
| `userInfo.id` | `userId` | 用户ID(从本地存储获取) |

#### 字段分析

**✅ 实际传递的字段：**
- `serviceType`: 服务类型 (hospitalization)
- `status`: 订单状态 (pending)
- `price`: 价格 (陪护师价格 × 天数)
- `remarks`: 备注 (来自特殊需求)
- `userId`: 用户ID (从本地存储获取)
- `staffId`: 陪护师ID (用户选择)
- `hospitalId`: 医院ID (用户选择)
- `patientId`: 患者ID (用户选择)
- `patientName`: 患者姓名 (从患者信息获取)
- `patientGender`: 患者性别 (中文转换后)
- `patientIdCard`: 患者身份证 (从患者信息获取)
- `patientPhone`: 患者手机号 (从患者信息获取)
- `patientBirthday`: 患者生日 (从患者信息获取)
- `locationInfo.address`: 陪护地点地址 (用户输入或地图选点)
- `locationInfo.room`: 房间号 (住院服务特有)
- `attachments`: 附件 (null，表单中没有此功能)
- `appointmentTime`: 预约时间 (日期转换为ISO格式)
- `duration`: 陪护天数 (住院服务特有)
- `serviceDetails.items`: 服务项目列表 (包含陪护描述)
- `serviceDetails.requirements`: 特殊需求 (与remarks重复)

**❌ 未传递的字段：**
- 无 (住院服务表单字段完整)

**🔄 自动生成的字段：**
- `price`: 从陪护师价格和天数计算得出
- `userId`: 从本地存储的用户信息获取
- `appointmentTime`: 由日期字段转换为ISO格式
- `serviceDetails.items`: 根据特殊需求生成服务项目

#### 返回响应
```json
{
  "success": true,
  "data": {
    "id": 9,
    "orderNo": "SRV20250728171929U45LA0",
    "userId": 6,
    "staffId": 2,
    "hospitalId": 2,
    "serviceType": "hospitalization",
    "serviceDetails": {
      "items": [
        {
          "desc": "好好照顾",
          "type": "hospitalization",
          "staffId": 2
        }
      ],
      "requirements": "好好照顾"
    },
    "status": "pending",
    "appointmentTime": "2025-07-29T00:00:00.000Z",
    "duration": 2,
    "price": "200.00",
    "paymentInfo": null,
    "patientInfo": {
      "name": "病人1",
      "gender": "male",
      "idCard": "177777188809122222",
      "phone": "18700000001",
      "birthday": "2014-07-28"
    },
    "patientId": "3",
    "locationInfo": {
      "room": "101",
      "address": "陕西省西安市未央区张家堡街道凤城八路158号风景御园18栋1单元10102室（岚嵉叙传统茶(凤城八路店)）"
    },
    "rating": null,
    "review": null,
    "attachments": null,
    "completedAt": null,
    "createdBy": 6,
    "updatedBy": null,
    "createdAt": "2025-07-28T09:19:29.651Z",
    "updatedAt": "2025-07-28T09:19:29.651Z",
    "deletedAt": null,
    "user": {
      "id": 6,
      "username": "wx_QW9UdY",
      "mobile": "19800000010"
    },
    "staff": {
      "id": 2,
      "type": "companion",
      "serviceScore": "5.0"
    },
    "hospital": {
      "id": 2,
      "name": "测试医院1",
      "level": "三级甲等",
      "address": {
        "city": "西安",
        "detail": "雁塔区丈八四路",
        "province": "陕西"
      }
    },
    "creator": {
      "id": 6,
      "username": "wx_QW9UdY"
    }
  },
  "message": "服务订单创建成功"
}
```

#### 订单详情页显示字段分析

**✅ 需要显示的信息：**

**基本信息：**
- 订单号：`orderNo`
- 订单状态：`status`
- 创建时间：`createdAt`
- 价格：`price`

**服务信息：**
- 服务类型：`serviceType` (显示为"住院陪护")
- 预约时间：`appointmentTime`
- 陪护天数：`duration`
- 特殊需求：`serviceDetails.requirements`

**患者信息：**
- 姓名：`patientInfo.name`
- 性别：`patientInfo.gender` (需要转换为中文显示)
- 身份证：`patientInfo.idCard`
- 手机号：`patientInfo.phone`
- 生日：`patientInfo.birthday`

**陪护信息：**
- 陪护地点：`locationInfo.address`
- 房间号：`locationInfo.room`
- 陪护师：`staff` 相关信息
- 医院：`hospital` 相关信息

**用户信息：**
- 用户名：`user.username`
- 手机号：`user.mobile`

**❌ 不需要显示的信息：**
- 无 (住院服务所有字段都需要显示)

---

### 3. 买药服务 (medicine)

#### 表单项核对

**✅ 表单中的字段：**
1. **医院选择** → `hospitalId`
2. **医护人员选择** → `staffId`
3. **患者选择** → `patientId`, `patientName`, `patientGender`, `patientIdCard`, `patientPhone`, `patientBirthday`
4. **药品选择** → `medicines` (药品列表，包含药品ID、数量)
5. **处方图片** → `prescriptionUrl` (上传后获得URL)
6. **配送地址** → `deliveryInfo.address`
7. **备注** → `remarks` (可选)

**❌ 表单中没有的字段：**
- 预约时间 (买药服务不需要预约时间)
- 陪护时长 (买药服务不需要)

#### 实际提交字段

**服务订单 (POST /orders/service_orders)：**
```json
{
  "serviceType": "proxy",
  "status": "pending",
  "price": 20,
  "remarks": "撒旦范德萨发生",
  "userId": 6,
  "staffId": 2,
  "hospitalId": 2,
  "patientId": 3,
  "patientName": "病人1",
  "patientGender": "男",
  "patientIdCard": "177777188809122222",
  "patientPhone": "18700000001",
  "patientBirthday": "2014-07-28",
  "locationInfo": null,
  "attachments": null,
  "appointmentTime": null,
  "duration": null,
  "serviceDetails": {
    "items": [
      {
        "desc": "撒旦范德萨发生",
        "name": "测试药品",
        "type": "medicine",
        "price": 10,
        "quantity": 2
      }
    ]
  }
}
```

**药品订单 (POST /medicine_orders)：**
```json
{
  "orderId": 16,
  "medicines": [
    {
      "medicineId": 5,
      "quantity": 2
    }
  ],
  "prescriptionUrl": "http://127.0.0.1:3000/uploads/diaries/diary-1753698947410-247317281.png",
  "userId": 6,
  "remarks": "撒旦范德萨发生",
  "deliveryInfo": {
    "address": "山西省太原市"
  }
}
```

#### 字段映射关系

| 表单字段 | 服务订单字段 | 药品订单字段 | 说明 |
|----------|--------------|--------------|------|
| `hospitals[hospitalIndex].id` | `hospitalId` | - | 医院ID |
| `selectedStaffId` | `staffId` | - | 医护人员ID |
| `selectedPatient.patient_id` | `patientId` | - | 患者ID |
| `selectedPatient.name` | `patientName` | - | 患者姓名 |
| `patientGender` (转换后) | `patientGender` | - | 患者性别(中文) |
| `selectedPatient.id_card` | `patientIdCard` | - | 患者身份证 |
| `selectedPatient.phone` | `patientPhone` | - | 患者手机号 |
| `selectedPatient.birthday` | `patientBirthday` | - | 患者生日 |
| `selectedMedicines` | `serviceDetails.items` | `medicines` | 药品列表 |
| `prescriptionImages[0]` | - | `prescriptionUrl` | 处方图片URL |
| `deliveryInfo.address` | - | `deliveryInfo.address` | 配送地址 |
| `remarks` | `remarks` | `remarks` | 备注 |
| `totalPrice` | `price` | - | 总价格(药品价格×数量) |
| `userInfo.id` | `userId` | `userId` | 用户ID |

#### 字段分析

**✅ 实际传递的字段：**

**服务订单字段：**
- `serviceType`: 服务类型 (proxy)
- `status`: 订单状态 (pending)
- `price`: 总价格 (药品价格×数量)
- `remarks`: 备注 (来自表单备注)
- `userId`: 用户ID (从本地存储获取)
- `staffId`: 医护人员ID (用户选择)
- `hospitalId`: 医院ID (用户选择)
- `patientId`: 患者ID (用户选择)
- `patientName`: 患者姓名 (从患者信息获取)
- `patientGender`: 患者性别 (中文转换后)
- `patientIdCard`: 患者身份证 (从患者信息获取)
- `patientPhone`: 患者手机号 (从患者信息获取)
- `patientBirthday`: 患者生日 (从患者信息获取)
- `locationInfo`: null (买药服务不需要地点信息)
- `attachments`: null (表单中没有此功能)
- `appointmentTime`: null (买药服务不需要预约时间)
- `duration`: null (买药服务不需要时长)
- `serviceDetails.items`: 药品项目列表 (包含药品描述、名称、类型、价格、数量)

**药品订单字段：**
- `orderId`: 关联的服务订单ID
- `medicines`: 药品列表 (包含药品ID和数量)
- `prescriptionUrl`: 处方图片URL (上传后获得)
- `userId`: 用户ID (从本地存储获取)
- `remarks`: 备注 (来自表单备注)
- `deliveryInfo.address`: 配送地址 (用户输入)

**❌ 未传递的字段：**
- `locationInfo`: 买药服务不需要地点信息
- `appointmentTime`: 买药服务不需要预约时间
- `duration`: 买药服务不需要时长

**🔄 自动生成的字段：**
- `price`: 从药品价格和数量计算得出
- `userId`: 从本地存储的用户信息获取
- `serviceDetails.items`: 根据选择的药品生成服务项目
- `prescriptionUrl`: 处方图片上传后获得的URL

#### 返回响应

**服务订单返回：**
```json
{
  "success": true,
  "data": {
    "id": 16,
    "orderNo": "SRV20250728183547DWADP3",
    "userId": 6,
    "staffId": 2,
    "hospitalId": 2,
    "serviceType": "proxy",
    "serviceDetails": {
      "items": [
        {
          "desc": "撒旦范德萨发生",
          "name": "测试药品",
          "type": "medicine",
          "price": 10,
          "quantity": 2
        }
      ]
    },
    "status": "pending",
    "appointmentTime": null,
    "duration": null,
    "price": "20.00",
    "paymentInfo": null,
    "patientInfo": {
      "name": "病人1",
      "gender": "male",
      "idCard": "177777188809122222",
      "phone": "18700000001",
      "birthday": "2014-07-28"
    },
    "patientId": "3",
    "locationInfo": null,
    "rating": null,
    "review": null,
    "attachments": null,
    "completedAt": null,
    "createdBy": 6,
    "updatedBy": null,
    "createdAt": "2025-07-28T10:35:47.449Z",
    "updatedAt": "2025-07-28T10:35:47.449Z",
    "deletedAt": null,
    "user": {
      "id": 6,
      "username": "wx_QW9UdY",
      "mobile": "19800000010"
    },
    "staff": {
      "id": 2,
      "type": "companion",
      "serviceScore": "5.0"
    },
    "hospital": {
      "id": 2,
      "name": "测试医院1",
      "level": "三级甲等",
      "address": {
        "city": "西安",
        "detail": "雁塔区丈八四路",
        "province": "陕西"
      }
    },
    "creator": {
      "id": 6,
      "username": "wx_QW9UdY"
    }
  },
  "message": "服务订单创建成功"
}
```

**药品订单返回：**
```json
{
  "success": true,
  "data": {
    "id": 6,
    "orderId": 16,
    "prescriptionUrl": "http://127.0.0.1:3000/uploads/diaries/diary-1753698947410-247317281.png",
    "status": "pending",
    "deliveryInfo": {
      "address": "山西省太原市"
    },
    "createdBy": 6,
    "updatedBy": null,
    "createdAt": "2025-07-28T10:35:47.489Z",
    "updatedAt": "2025-07-28T10:35:47.489Z",
    "deletedAt": null,
    "medicines": [
      {
        "id": 5,
        "name": "测试药品",
        "price": "10.00",
        "prescriptionRequired": true,
        "MedicineOrderMedicine": {
          "quantity": 2
        }
      }
    ],
    "order": {
      "id": 16,
      "orderNo": "SRV20250728183547DWADP3",
      "status": "pending",
      "user_id": 6,
      "user": {
        "id": 6,
        "username": "wx_QW9UdY",
        "mobile": "19800000010"
      }
    },
    "items": [
      {
        "medicine": {
          "id": 5,
          "name": "测试药品",
          "price": "10.00",
          "prescriptionRequired": true
        },
        "quantity": 2,
        "unitPrice": "10.00",
        "amount": 20
      }
    ],
    "totalPrice": 20
  },
  "message": "药品订单创建成功"
}
```

#### 订单详情页显示字段分析

**✅ 需要显示的信息：**

**基本信息：**
- 订单号：`orderNo` (来自服务订单)
- 订单状态：`status` (来自服务订单)
- 创建时间：`createdAt` (来自服务订单)
- 价格：`price` (来自服务订单)

**服务信息：**
- 服务类型：`serviceType` (显示为"买药服务")
- 药品列表：`serviceDetails.items` (来自服务订单)
- 备注：`remarks` (来自服务订单)

**患者信息：**
- 姓名：`patientInfo.name` (来自服务订单)
- 性别：`patientInfo.gender` (需要转换为中文显示)
- 身份证：`patientInfo.idCard` (来自服务订单)
- 手机号：`patientInfo.phone` (来自服务订单)
- 生日：`patientInfo.birthday` (来自服务订单)

**买药信息：**
- 处方图片：`prescriptionUrl` (来自药品订单)
- 配送地址：`deliveryInfo.address` (来自药品订单)
- 药品详情：`items` (来自药品订单，包含药品名称、数量、单价、小计)
- 总价格：`totalPrice` (来自药品订单)

**医护人员信息：**
- 医护人员：`staff` 相关信息 (来自服务订单)
- 医院：`hospital` 相关信息 (来自服务订单)

**用户信息：**
- 用户名：`user.username` (来自服务订单)
- 手机号：`user.mobile` (来自服务订单)

**❌ 不需要显示的信息：**
- 预约时间：`appointmentTime` (买药服务不需要)
- 陪护时长：`duration` (买药服务不需要)
- 地点信息：`locationInfo` (买药服务不需要)

---

### 4. 挂号服务 (registration)

#### 表单项核对

**✅ 表单中的字段：**
1. **医院选择** → `hospitalId`
2. **科室选择** → `department`
3. **医护人员选择** → `staffId`
4. **患者选择** → `patientId`, `patientName`, `patientGender`, `patientIdCard`, `patientPhone`, `patientBirthday`
5. **挂号日期** → `visitDate`
6. **特殊需求** → `demand` → 映射到 `remarks` 和 `serviceDetails.requirements`

**❌ 表单中没有的字段：**
- 时间段 (time_slot) - 表单中没有选择时间段
- 症状描述 (symptoms) - 表单中没有症状输入
- 医生姓名 (doctor_name) - 表单中选择的是医护人员，不是具体医生

#### 实际提交字段

**服务订单 (POST /orders/service_orders)：**
```json
{
  "serviceType": "registration",
  "status": "pending",
  "price": 50.00,
  "remarks": "特殊需求说明",
  "userId": 6,
  "staffId": 2,
  "hospitalId": 2,
  "department": "内科",
  "visitDate": "2025-07-29",
  "description": "挂号服务 - 测试医院1 内科",
  "patientId": 3,
  "patientName": "病人1",
  "patientGender": "男",
  "patientIdCard": "177777188809122222",
  "patientPhone": "18700000001",
  "patientBirthday": "2014-07-28",
  "serviceDetails": {
    "items": [
      {
        "name": "挂号费",
        "type": "registration",
        "desc": "测试医院1 内科 挂号服务",
        "price": 50.00,
        "quantity": 1
      }
    ],
    "requirements": "特殊需求说明"
  }
}
```

**挂号订单 (POST /orders/registrations)：**
```json
{
  "patientId": 6,
  "hospitalId": 2,
  "department": "内科",
  "visitDate": "2025-07-29",
  "registrationNo": "",
  "remarks": "特殊需求说明",
  "orderId": 17,
  "patientName": "wx_QW9UdY",
  "staffId": 2
}
```

#### 字段映射关系

| 表单字段 | 服务订单字段 | 挂号订单字段 | 说明 |
|----------|--------------|--------------|------|
| `hospitals[hospitalIndex].id` | `hospitalId` | `hospitalId` | 医院ID |
| `departments[departmentIndex].value` | `department` | `department` | 科室名称 |
| `selectedStaffId` | `staffId` | `staffId` | 医护人员ID |
| `selectedPatient.patient_id` | `patientId` | - | 患者ID |
| `selectedPatient.name` | `patientName` | - | 患者姓名 |
| `patientGender` (转换后) | `patientGender` | - | 患者性别(中文) |
| `selectedPatient.id_card` | `patientIdCard` | - | 患者身份证 |
| `selectedPatient.phone` | `patientPhone` | - | 患者手机号 |
| `selectedPatient.birthday` | `patientBirthday` | - | 患者生日 |
| `date` | `visitDate` | `visitDate` | 挂号日期 |
| `demand` | `remarks` | `remarks` | 特殊需求 |
| `demand` | `serviceDetails.requirements` | - | 特殊需求(重复) |
| `demand` | `serviceDetails.items[0].desc` | - | 特殊需求(项目描述) |
| 固定价格 | `price` | - | 挂号费(固定50元) |
| `userInfo.id` | `userId` | `patientId` | 用户ID |

#### 字段分析

**✅ 实际传递的字段：**

**服务订单字段：**
- `serviceType`: 服务类型 (registration)
- `status`: 订单状态 (pending)
- `price`: 价格 (固定挂号费50元)
- `remarks`: 备注 (来自特殊需求)
- `userId`: 用户ID (从本地存储获取)
- `staffId`: 医护人员ID (用户选择)
- `hospitalId`: 医院ID (用户选择)
- `department`: 科室名称 (用户选择)
- `visitDate`: 挂号日期 (用户选择)
- `description`: 服务描述 (自动生成)
- `patientId`: 患者ID (用户选择)
- `patientName`: 患者姓名 (从患者信息获取)
- `patientGender`: 患者性别 (中文转换后)
- `patientIdCard`: 患者身份证 (从患者信息获取)
- `patientPhone`: 患者手机号 (从患者信息获取)
- `patientBirthday`: 患者生日 (从患者信息获取)
- `serviceDetails.items`: 服务项目列表 (包含挂号费项目)
- `serviceDetails.requirements`: 特殊需求 (与remarks重复)

**挂号订单字段：**
- `patientId`: 患者ID (从本地存储获取)
- `hospitalId`: 医院ID (用户选择)
- `department`: 科室名称 (用户选择)
- `visitDate`: 挂号日期 (用户选择)
- `registrationNo`: 挂号单号 (系统自动生成)
- `remarks`: 备注 (来自特殊需求)
- `orderId`: 关联的服务订单ID
- `patientName`: 患者姓名 (从服务订单用户信息获取)
- `staffId`: 医护人员ID (用户选择)

**❌ 未传递的字段：**
- `time_slot`: 时间段 (表单中没有选择)
- `symptoms`: 症状描述 (表单中没有输入)
- `doctor_name`: 医生姓名 (表单中选择的是医护人员)
- `patient_id_card`: 患者身份证 (挂号订单中没有传递)
- `his_info`: HIS系统信息 (需要医院系统集成)

**🔄 自动生成的字段：**
- `price`: 固定挂号费50元
- `userId`: 从本地存储的用户信息获取
- `description`: 根据医院和科室自动生成
- `registrationNo`: 系统自动生成的挂号单号
- `serviceDetails.items`: 根据挂号服务生成服务项目

#### 返回响应

**服务订单返回：**
```json
{
  "success": true,
  "data": {
    "id": 17,
    "orderNo": "SRV20250728190000REG123",
    "userId": 6,
    "staffId": 2,
    "hospitalId": 2,
    "serviceType": "registration",
    "serviceDetails": {
      "items": [
        {
          "name": "挂号费",
          "type": "registration",
          "desc": "测试医院1 内科 挂号服务",
          "price": 50.00,
          "quantity": 1
        }
      ],
      "requirements": "特殊需求说明"
    },
    "status": "pending",
    "appointmentTime": null,
    "duration": null,
    "price": "50.00",
    "paymentInfo": null,
    "patientInfo": {
      "name": "病人1",
      "gender": "male",
      "idCard": "177777188809122222",
      "phone": "18700000001",
      "birthday": "2014-07-28"
    },
    "patientId": "3",
    "locationInfo": null,
    "rating": null,
    "review": null,
    "attachments": null,
    "completedAt": null,
    "createdBy": 6,
    "updatedBy": null,
    "createdAt": "2025-07-28T11:00:00.000Z",
    "updatedAt": "2025-07-28T11:00:00.000Z",
    "deletedAt": null,
    "user": {
      "id": 6,
      "username": "wx_QW9UdY",
      "mobile": "19800000010"
    },
    "staff": {
      "id": 2,
      "type": "companion",
      "serviceScore": "5.0"
    },
    "hospital": {
      "id": 2,
      "name": "测试医院1",
      "level": "三级甲等",
      "address": {
        "city": "西安",
        "detail": "雁塔区丈八四路",
        "province": "陕西"
      }
    },
    "creator": {
      "id": 6,
      "username": "wx_QW9UdY"
    }
  },
  "message": "服务订单创建成功"
}
```

**挂号订单返回：**
```json
{
  "success": true,
  "data": {
    "id": 8,
    "orderId": 17,
    "hospitalId": 2,
    "department": "内科",
    "doctor_name": null,
    "visitDate": "2025-07-29",
    "time_slot": null,
    "patientName": "wx_QW9UdY",
    "patient_id_card": null,
    "symptoms": null,
    "status": "pending",
    "registrationNo": "REG202507281900008",
    "his_info": null,
    "createdAt": "2025-07-28T11:00:00.000Z",
    "updatedAt": "2025-07-28T11:00:00.000Z"
  },
  "message": "挂号订单创建成功"
}
```

#### 订单详情页显示字段分析

**✅ 需要显示的信息：**

**基本信息：**
- 订单号：`orderNo` (来自服务订单)
- 挂号单号：`registrationNo` (来自挂号订单)
- 订单状态：`status` (来自服务订单)
- 创建时间：`createdAt` (来自服务订单)
- 价格：`price` (来自服务订单)

**挂号信息：**
- 挂号日期：`visitDate` (来自挂号订单)
- 科室：`department` (来自挂号订单)
- 医院：`hospital` 相关信息 (来自服务订单)
- 医护人员：`staff` 相关信息 (来自服务订单)

**患者信息：**
- 姓名：`patientInfo.name` (来自服务订单)
- 性别：`patientInfo.gender` (需要转换为中文显示)
- 身份证：`patientInfo.idCard` (来自服务订单)
- 手机号：`patientInfo.phone` (来自服务订单)
- 生日：`patientInfo.birthday` (来自服务订单)

**用户信息：**
- 用户名：`user.username` (来自服务订单)
- 手机号：`user.mobile` (来自服务订单)

**❌ 不需要显示的信息：**
- 预约时间：`appointmentTime` (挂号服务使用visitDate)
- 陪护时长：`duration` (挂号服务不需要)
- 地点信息：`locationInfo` (挂号服务不需要)
- 时间段：`time_slot` (表单中没有选择)
- 症状描述：`symptoms` (表单中没有输入)
- 医生姓名：`doctor_name` (表单中选择的是医护人员)

**📱 页面布局：**
```
📋 订单状态卡片
📋 基本信息卡片 (下单时间、订单金额、挂号单号)
👤 患者信息卡片
🏥 医院信息卡片
👩‍⚕️ 医护人员信息卡片
📅 挂号信息卡片 (挂号日期、科室)
📝 需求说明卡片 (如果有)
🔧 订单操作卡片
```

---

## 通用字段说明

### 订单状态 (status)
- `pending`: 待支付
- `paid`: 已支付
- `in_progress`: 进行中
- `completed`: 已完成
- `cancelled`: 已取消

### 服务类型 (serviceType)
- `companion`: 陪诊服务
- `hospitalize`: 住院陪护
- `medicine`: 买药服务
- `registration`: 挂号服务

### 时间格式
- 所有时间字段使用 ISO 8601 格式：`YYYY-MM-DDTHH:mm:ss.sssZ`
- 前端显示时需要转换为本地时间格式

### 性别字段处理
- 后端存储：`male` / `female`
- 前端显示：`男` / `女`

## 订单详情页显示规则

### 页面结构
订单详情页采用M3卡片风格布局，包含以下主要区域：
1. **顶部导航栏**：使用`global-navbar`组件，显示"订单详情"标题和返回按钮
2. **状态卡片**：突出显示订单状态和服务类型
3. **信息卡片**：按功能分组显示订单信息
4. **操作按钮**：根据订单状态和用户角色显示相应操作

### 导航栏配置
- **组件**：`global-navbar`
- **标题**：订单详情
- **返回按钮**：显示
- **文字颜色**：`#000000`
- **背景颜色**：`#ffffff`
- **行为**：使用`navbarBehavior`处理导航栏交互

### 通用显示字段
所有订单类型都需要显示：
1. 订单号 (`orderNo`)
2. 订单状态 (`status`)
3. 创建时间 (`createdAt`)
4. 价格 (`price`)
5. 患者信息 (`patientInfo`)
6. 医院信息 (`hospital`)
7. 医护人员信息 (`staff`)

### 特定显示字段
根据 `serviceType` 显示不同的特定信息：

#### 陪诊服务 (companion)
**✅ 显示字段：**
- 预约时间 (`appointmentTime`)
- 陪诊地点 (`locationInfo.address`)
- 患者信息 (`patientInfo`)
- 陪诊师信息 (`staff`)
- 医院信息 (`hospital`)
- 需求说明 (`serviceDetails.requirements`)

**❌ 不显示字段：**
- 房间号 (`locationInfo.room`) - 此服务类型不需要
- 陪诊时长 (`duration`) - 此服务类型不需要

**📱 页面布局：**
```
📋 订单状态卡片
📋 基本信息卡片 (下单时间、订单金额、预约时间)
👤 患者信息卡片
📍 服务地点卡片 (陪诊地点)
🏥 医院信息卡片
👩‍⚕️ 医护人员信息卡片
📝 需求说明卡片 (如果有)
🔧 订单操作卡片
```

#### 住院陪护 (hospitalization)
**✅ 显示字段：**
- 预约时间 (`appointmentTime`)
- 陪护天数 (`duration`)
- 陪护地点 (`locationInfo.address`)
- 房间号 (`locationInfo.room`)
- 患者信息 (`patientInfo`)
- 陪护师信息 (`staff`)
- 医院信息 (`hospital`)
- 特殊需求 (`serviceDetails.requirements`)

**❌ 不显示字段：**
- 无 (住院服务所有字段都需要显示)

**📱 页面布局：**
```
📋 订单状态卡片
📋 基本信息卡片 (下单时间、订单金额、预约时间、陪护天数)
👤 患者信息卡片
📍 服务地点卡片 (陪护地点、房间号)
🏥 医院信息卡片
👩‍⚕️ 医护人员信息卡片
📝 需求说明卡片 (如果有)
🔧 订单操作卡片
```

#### 买药服务 (proxy)
**✅ 显示字段：**
- 药品列表 (`serviceDetails.items`)
- 配送地址 (`deliveryInfo.address`)
- 处方图片 (`prescriptionUrl`)
- 患者信息 (`patientInfo`)
- 医护人员信息 (`staff`)
- 医院信息 (`hospital`)
- 备注 (`serviceDetails.requirements`)

**❌ 不显示字段：**
- 预约时间 (`appointmentTime`) - 买药服务不需要
- 陪护时长 (`duration`) - 买药服务不需要
- 地点信息 (`locationInfo`) - 买药服务不需要

**📱 页面布局：**
```
📋 订单状态卡片
📋 基本信息卡片 (下单时间、订单金额)
👤 患者信息卡片
💊 买药信息卡片 (药品列表、处方图片、配送地址)
🏥 医院信息卡片
👩‍⚕️ 医护人员信息卡片
📝 需求说明卡片 (如果有)
🔧 订单操作卡片
```

**🔄 数据兼容处理：**
- 处方图片 (`prescriptionUrl`) 可能来自药品订单 (`medicineOrders[0].prescriptionUrl`)
- 配送地址 (`deliveryInfo`) 可能来自药品订单 (`medicineOrders[0].deliveryInfo`)

#### 挂号服务 (registration)
**✅ 显示字段：**
- 挂号日期 (`visitDate`)
- 科室信息 (`department`)
- 挂号单号 (`registrationNo`)
- 医院信息 (`hospital`)
- 医护人员信息 (`staff`)
- 患者信息 (`patientInfo`)
- 用户信息 (`user`)
- 需求说明 (`serviceDetails.requirements`)

**❌ 不显示字段：**
- 预约时间 (`appointmentTime`) - 挂号服务使用visitDate
- 陪护时长 (`duration`) - 挂号服务不需要
- 地点信息 (`locationInfo`) - 挂号服务不需要
- 时间段 (`time_slot`) - 表单中没有选择
- 症状描述 (`symptoms`) - 表单中没有输入
- 医生姓名 (`doctor_name`) - 表单中选择的是医护人员

**📱 页面布局：**
```
📋 订单状态卡片
📋 基本信息卡片 (下单时间、订单金额、挂号单号)
👤 患者信息卡片
🏥 医院信息卡片
👩‍⚕️ 医护人员信息卡片
📅 挂号信息卡片 (挂号日期、科室)
📝 需求说明卡片 (如果有)
🔧 订单操作卡片
```

### 操作按钮规则

#### 按钮类型
1. **主操作按钮** (绿色 `#4C662B`)：去支付、开始服务、完成服务等
2. **警告操作按钮** (红色 `#BA1A1A`)：取消订单、申请退款、申请售后
3. **次要操作按钮** (浅绿色 `#CDEDA3`)：联系服务人员、去评价、查看评价

#### 操作处理逻辑
- **退款、退单、取消订单**：点击后只提示"联系客服处理"，不实际请求API
- **其他操作**：保持原有业务逻辑不变

#### 按钮显示规则
根据订单状态 (`status`) 和用户角色 (`userType`) 动态显示：
- `patient` 用户：去支付、取消订单、申请退款、申请售后、确认收货、联系服务人员、去评价
- `staff/medical_staff` 用户：分配服务人员、开始服务、完成服务、发货、备货、查看评价

### 视觉设计规范

#### 颜色方案
- **主色**：`#4C662B` (深绿色)
- **背景色**：`#F9FAEF` (浅米色)
- **卡片背景**：`#FFFFFF` (白色)
- **边框色**：`#F3F4E9` (浅灰色)
- **警告色**：`#BA1A1A` (红色)
- **次要色**：`#CDEDA3` (浅绿色)

#### 卡片样式
- 圆角：`16rpx`
- 阴影：`0 2rpx 8rpx rgba(0, 0, 0, 0.08)`
- 边框：`1rpx solid #F3F4E9`
- 内边距：`24rpx`
- 外边距：`20rpx`

#### 状态卡片
- 渐变背景：`linear-gradient(135deg, #4C662B 0%, #586249 100%)`
- 白色文字
- 包含服务类型图标和状态信息

---

## 字段差异对比

### 陪诊服务 vs 住院陪护
| 字段 | 陪诊服务 | 住院陪护 | 说明 |
|------|----------|----------|------|
| 房间号 | ❌ 不需要 | ✅ 需要 | 陪诊在门诊，住院在病房 |
| 陪护天数 | ❌ 不需要 | ✅ 需要 | 陪诊按次收费，住院按天收费 |
| 预约时间 | ✅ 需要 | ✅ 需要 | 都需要预约时间 |
| 服务类型 | `companion` | `hospitalization` | 不同的服务类型标识 |
| 价格计算 | 陪诊师价格 | 陪诊师价格 × 天数 | 住院按天数计算 |
| 服务项目 | 空数组 | 包含陪护描述 | 住院有具体的服务项目描述 |

### 买药服务 vs 其他服务
| 字段 | 买药服务 | 其他服务 | 说明 |
|------|----------|----------|------|
| 配送地址 | ✅ 需要 | ❌ 不需要 | 买药需要配送 |
| 处方图片 | ✅ 需要 | ❌ 不需要 | 买药需要处方 |
| 药品列表 | ✅ 需要 | ❌ 不需要 | 买药特有字段 |
| 预约时间 | ❌ 不需要 | ✅ 需要 | 买药不需要预约时间 |
| 陪护时长 | ❌ 不需要 | ✅ 需要 | 买药不需要时长 |
| 地点信息 | ❌ 不需要 | ✅ 需要 | 买药不需要地点信息 |
| 服务类型 | `proxy` | `companion`/`hospitalization` | 买药使用proxy类型 |

### 挂号服务 vs 其他服务
| 字段 | 挂号服务 | 其他服务 | 说明 |
|------|----------|----------|------|
| 挂号日期 | ✅ 需要 | ❌ 不需要 | 挂号使用visitDate |
| 科室信息 | ✅ 需要 | ❌ 不需要 | 挂号特有字段 |
| 挂号单号 | ✅ 需要 | ❌ 不需要 | 挂号特有字段 |
| 预约时间 | ❌ 不需要 | ✅ 需要 | 挂号使用visitDate |
| 陪护时长 | ❌ 不需要 | ✅ 需要 | 挂号不需要时长 |
| 地点信息 | ❌ 不需要 | ✅ 需要 | 挂号不需要地点信息 |
| 服务类型 | `registration` | `companion`/`hospitalization`/`proxy` | 挂号使用registration类型 |

---

## 更新记录

- 2025-07-28: 添加陪诊服务订单文档，详细记录实际传递和未传递的字段
- 2025-07-28: 完善住院陪护订单文档，根据实际API返回数据补充完整字段记录
- 2025-07-28: 完善买药服务订单文档，根据实际API返回数据补充完整字段记录，包括服务订单和药品订单的双重结构
- 2025-07-28: 完善订单详情页显示规则文档，包括页面结构、操作按钮规则、视觉设计规范，并添加顶部导航栏配置
- 2025-07-28: 完善挂号服务订单文档，根据表单和API分析补充完整字段记录，包括服务订单和挂号订单的双重结构 