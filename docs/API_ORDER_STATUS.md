# 订单类型状态位与流转顺序

## 1. 服务订单（ServiceOrder）

### 状态位（status 字段）
- `pending`      待处理
- `paid`         已支付
- `assigned`     已分配
- `in_progress`  进行中
- `completed`    已完成
- `cancelled`    已取消
- `refunded`     已退款

### 流转顺序
```mermaid
graph LR
A[待处理 pending] --> B[已支付 paid]
B --> C[已分配 assigned]
C --> D[进行中 in_progress]
D --> E[已完成 completed]
A --> F[已取消 cancelled]
B --> F
C --> F
D --> F
B --> G[已退款 refunded]
C --> G
```

---

## 2. 挂号订单（Registration）

### 状态位（status 字段）
- `pending`      待处理
- `registered`   已挂号
- `completed`    已完成
- `cancelled`    已取消

### 流转顺序
```mermaid
graph LR
A[待处理 pending] --> B[已挂号 registered]
B --> C[已完成 completed]
A --> D[已取消 cancelled]
B --> D
```

### 重要说明
**挂号订单完成机制：**
- 挂号订单的完成不需要单独的API请求
- 当关联的服务订单状态变为 `completed` 时，挂号订单自动变为 `completed`
- 挂号订单详情页不显示"完成挂号"按钮，统一使用服务订单的"完成服务"按钮
- 这样确保服务订单和挂号订单状态的一致性，避免重复操作

---

## 3. 药品订单（MedicineOrder）

### 状态位（status 字段）
- `pending`      待处理
- `preparing`    配药中
- `shipped`      已发货
- `delivered`    已送达
- `cancelled`    已取消

> 注：如需扩展如 paid/completed/refunded 等状态，需同步数据库设计和前后端实现。

### 流转顺序
```mermaid
graph LR
A[待处理 pending] --> B[配药中 preparing]
B --> C[已发货 shipped]
C --> D[已送达 delivered]
A --> E[已取消 cancelled]
B --> E
C --> E
```

---

## 4. 其他类型（如陪诊、住院等）
- 状态流转一般与服务订单类似，主要是 `pending` → `paid` → `assigned` → `in_progress` → `completed`，中间可 `cancelled` 或 `refunded`。

---

## 总结表

| 订单类型     | 状态流转顺序（主流程）                                      |
|--------------|----------------------------------------------------------|
| 服务订单     | pending → paid → assigned → in_progress → completed      |
| 挂号订单     | pending → registered → completed                         |
| 药品订单     | pending → preparing → shipped → delivered                |
| 通用         | 任何非完成状态下都可 cancelled                           |

---

> 本文档用于前后端统一订单状态流转，后端校验、前端展示、业务流转均应参照本表。如有变更需同步数据库设计。 

---

## 订单详情页操作按钮说明

- 订单详情页根据订单类型（服务、挂号、药品）和当前状态，动态显示可用操作按钮。
- 病患用户主要操作：去支付、取消订单、申请退款/售后、确认收货等。
- staff用户主要操作：分配、发货、确认挂号、完成服务/订单等。
- 具体按钮显示逻辑详见《API_ORDER_PAGE_DESIGN.md》第9节，后端如有状态流转变更需同步前端。 

---

## 5. 订单操作与消息推送设计

### 5.1 用户类型与可操作订单

| 用户类型         | 可操作订单类型         | 典型可操作行为（示例）         |
|------------------|-----------------------|-------------------------------|
| 病患/家属        | 服务、挂号、药品       | 去支付、取消、确认收货、申请退款/售后 |
| 医护/陪诊师（staff）| 服务、挂号、药品       | 分配、开始服务、发货、确认挂号、完成服务/订单 |
| 管理员           | 所有                   | 审核、强制取消、人工介入等      |

### 5.2 操作与消息推送规则

- **每次订单状态变更（如支付、取消、分配、发货、完成等），系统应自动创建一条消息，通知相关方。**
- 消息表结构参考 databaseDesign.md：
  - sender_id（发送者ID，可为操作人或系统）
  - receiver_id（接收者ID，通常为订单另一方）
  - order_id（关联订单）
  - content（消息内容）
  - is_read（是否已读）
  - created_at

#### 典型操作与消息示例

| 订单类型 | 操作人   | 操作行为         | 发送给         | 消息内容示例                         |
|----------|----------|------------------|--------------|--------------------------------------|
| 服务     | 病患     | 去支付           | staff        | “用户xxx已支付订单xxx，请尽快处理”   |
| 服务     | 病患     | 取消订单         | staff        | “用户xxx已取消订单xxx”               |
| 服务     | staff    | 分配服务人员     | 病患         | “您的订单xxx已分配陪诊师xxx”         |
| 服务     | staff    | 开始服务         | 病患         | “陪诊师xxx已开始为您服务（订单xxx）” |
| 服务     | staff    | 完成服务         | 病患         | “陪诊师xxx已完成订单xxx”             |
| 挂号     | 病患     | 取消订单         | staff        | “用户xxx已取消挂号订单xxx”           |
| 挂号     | staff    | 确认挂号         | 病患         | “您的挂号订单xxx已确认”              |
| 挂号     | staff    | 完成订单         | 病患         | “您的挂号订单xxx已完成”              |
| 药品     | staff    | 发货             | 病患         | “您的药品订单xxx已发货”              |
| 药品     | 病患     | 确认收货         | staff        | “用户xxx已确认收货（订单xxx）”       |
| 药品     | 病患     | 取消订单         | staff        | “用户xxx已取消药品订单xxx”           |
| 药品     | staff    | 完成订单         | 病患         | “您的药品订单xxx已完成”              |

- **系统自动消息**：如有需要，可由 sender_id 设为系统（如0或null），如“系统通知：订单xxx已被管理员取消”。
- **消息内容可根据业务自定义，建议包含订单号、操作人、操作类型等关键信息。**

---

> 本节用于前后端统一订单操作与消息推送逻辑，后端实现消息写入，前端展示消息提醒。如有变更需同步本文档。 

### 药品订单按钮逻辑
```javascript
if (type === 'medicine') {
  // 主服务订单状态决定操作权限
  // 只有当主服务订单状态为in_progress时，药品子订单才能操作
  if (parentOrderStatus && parentOrderStatus !== 'in_progress') {
    // 主服务订单未进行中，不显示任何操作按钮
    return [];
  }
  // 药品子订单状态决定具体操作
  const s = medicineOrderStatus || status;
  if (userRole === 'patient') {
    // 病患端：根据药品订单状态显示相应操作
    if (s === 'pending') return ['取消订单']; // 不显示支付，支付在主服务订单层面
    if (s === 'paid') return ['申请退款', '取消订单'];
    if (s === 'preparing') return ['申请退款', '取消订单'];
    if (s === 'shipped') return ['确认收货', '申请退款'];
    if (s === 'delivered') return ['申请售后'];
    if (s === 'completed') return ['申请售后'];
  } else if (userRole === 'staff' || userRole === 'medical_staff') {
    // staff端：根据药品订单状态显示相应操作
    if (s === 'pending') return ['备货'];
    if (s === 'paid') return ['备货'];
    if (s === 'preparing') return ['发货'];
    // 移除完成订单按钮，严格按照文档要求
    if (s === 'shipped') return []; // 不显示任何操作
    if (s === 'delivered') return []; // 不显示任何操作
  }
}
```

### 药品订单完成机制说明

**重要**：药品订单的完成不需要额外的API请求。

1. **药品配送流程**：`pending` → `preparing` → `shipped` → `delivered`
2. **服务完成流程**：当药品送达后，主服务订单仍为 `in_progress` 状态
3. **完成触发**：Staff 点击主服务订单的"完成服务"按钮
4. **API调用**：`ordersApi.completeOrder(orderId)` - 完成主服务订单
5. **自动完成**：后端自动将药品子订单状态设置为 `completed`

**优势**：
- 简化操作流程，避免重复的API调用
- 确保主服务订单和子订单状态的一致性
- 符合业务逻辑：药品配送是服务的一部分，服务完成时药品订单自然完成 

---

## 6. 完成服务操作权限变更说明

### 6.1 变更概述
**重要变更**：服务订单的"完成服务"操作权限从 staff 用户变更为病患用户。

### 6.2 变更详情

#### 变更前（原有设计）
- **操作人**：staff（医护/陪诊师）
- **操作时机**：服务进行中（`in_progress`）状态
- **操作结果**：服务订单状态变为 `completed`

#### 变更后（新设计）
- **操作人**：病患/家属
- **操作时机**：服务进行中（`in_progress`）状态
- **操作结果**：服务订单状态变为 `completed`

### 6.3 业务逻辑调整

#### 服务订单完成流程
1. **服务开始**：staff 点击"开始服务"，订单状态变为 `in_progress`
2. **服务进行**：staff 提供服务，病患接受服务
3. **服务完成**：病患确认服务完成，点击"完成服务"按钮
4. **状态变更**：订单状态从 `in_progress` 变为 `completed`

#### 消息推送调整
| 订单类型 | 操作人   | 操作行为         | 发送给         | 消息内容示例                         |
|----------|----------|------------------|--------------|--------------------------------------|
| 服务     | 病患     | 完成服务         | staff        | "用户xxx已确认完成服务（订单xxx）"   |

### 6.4 前端按钮显示逻辑调整

#### 服务订单按钮逻辑（更新后）
```javascript
if (type === 'service') {
  if (userRole === 'patient') {
    // 病患端：根据服务订单状态显示相应操作
    if (status === 'pending') return ['去支付', '取消订单'];
    if (status === 'paid') return ['申请退款', '取消订单'];
    if (status === 'assigned') return ['申请退款', '取消订单'];
    if (status === 'in_progress') return ['完成服务', '申请退款', '取消订单']; // 新增：病患可完成服务
    if (status === 'completed') return ['申请售后'];
  } else if (userRole === 'staff' || userRole === 'medical_staff') {
    // staff端：根据服务订单状态显示相应操作
    if (status === 'pending') return ['分配'];
    if (status === 'paid') return ['分配'];
    if (status === 'assigned') return ['开始服务'];
    if (status === 'in_progress') return []; // 移除：staff不再显示完成服务按钮
    if (status === 'completed') return [];
  }
}
```

### 6.5 影响范围

#### 直接影响
1. **服务订单**：完成服务操作权限从 staff 转移给病患
2. **挂号订单**：挂号订单的完成仍通过服务订单完成触发（保持不变）
3. **药品订单**：药品订单的完成仍通过服务订单完成触发（保持不变）

#### 间接影响
1. **消息推送**：完成服务的消息发送方向改变
2. **用户体验**：病患可以主动确认服务完成，提升服务体验
3. **业务逻辑**：更符合实际服务场景，病患是服务的最终确认方

### 6.6 实施注意事项

1. **前端更新**：需要更新订单详情页的按钮显示逻辑
2. **后端验证**：需要更新API权限验证，允许病患调用完成服务接口
3. **消息推送**：需要更新消息推送逻辑，调整发送方向
4. **测试验证**：需要全面测试新的完成服务流程

---

## 更新记录

### 2025-01-XX: 完成服务操作权限变更
1. **操作权限调整**：
   - 服务订单的"完成服务"操作从 staff 用户变更为病患用户
   - 病患可以在服务进行中状态时主动确认服务完成
   - staff 用户不再显示"完成服务"按钮

2. **业务逻辑优化**：
   - 更符合实际服务场景，病患是服务的最终确认方
   - 提升病患的服务体验和参与感
   - 简化服务完成流程

3. **消息推送调整**：
   - 完成服务的消息从病患发送给 staff
   - 消息内容调整为"用户xxx已确认完成服务（订单xxx）"

4. **影响范围**：
   - 挂号订单和药品订单的完成机制保持不变
   - 仍通过服务订单的完成来触发子订单的完成

### 2025-01-XX: 优化挂号订单设计
1. **移除冗余的完成按钮**：
   - 挂号订单不再显示单独的"完成挂号"按钮
   - 统一使用服务订单的"完成服务"按钮
   - 当服务订单完成时，挂号订单自动完成
   - 避免重复操作和状态不一致问题

2. **改进挂号单号显示**：
   - 确保挂号单号在基本信息卡片中正确显示
   - 兼容不同字段名的挂号单号（`registrationNo` 和 `registration_no`）
   - 如果挂号单号为空则不显示该字段

3. **统一状态流转**：
   - 挂号订单的完成通过服务订单的完成流程统一处理
   - 确保服务订单和挂号订单状态的一致性
   - 简化操作流程，提升用户体验 