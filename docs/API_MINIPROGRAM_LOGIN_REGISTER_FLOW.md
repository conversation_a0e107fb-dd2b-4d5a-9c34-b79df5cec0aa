# 小程序端极简静默登录+强制注册流程设计文档

## 一、整体流程说明

1. **静默登录**
   - 用户打开小程序，前端自动调用 `wx.login()` 获取 `code`。
   - 前端将 `code` 发送给服务端（如 `/system/auth/miniprogram_login`）。
   - 服务端用 `code` 换取 `openid`。
   - 服务端查找该 `openid` 是否已绑定手机号（`phone` 字段）和用户类型（`user_type` 字段）：
     - **已绑定**：服务端直接生成 `token`，返回给前端，用户自动登录进入应用。
     - **未绑定**：服务端返回“未注册”状态，前端自动跳转注册页。

2. **强制注册**
   - 注册页仅需用户选择用户类型（如“patient”/“medical_staff”等，严格对应 users.user_type）和填写手机号（users.phone）。
   - 用户提交后，前端再次调用 `wx.login()` 获取 `code`，将 `user_type`、`phone`、`code` 发送给服务端（如 `/system/auth/miniprogram_register`）。
   - 服务端用 `code` 换取 `openid`，完成手机号与 openid 绑定，创建用户（users 表），生成 `token` 并返回。
   - 前端收到 `token` 后自动登录，进入应用。

## 二、前后端交互流程

```mermaid
graph TD
A[小程序启动] --> B{wx.login 获取 code}
B --> C[发送 code 到服务端]
C --> D{服务端 code 换 openid}
D --> E{openid 是否已绑定手机号?}
E -- 是 --> F[生成 token 返回，前端自动登录]
E -- 否 --> G[返回未注册状态，前端跳转注册页]
G --> H[用户选择类型+填写手机号]
H --> I{注册提交，带 user_type+phone+code}
I --> J[服务端 code 换 openid，绑定手机号，生成 token]
J --> K[返回 token，前端自动登录]
```

## 三、页面与交互

### 1. 登录页（静默，无需用户操作）
- 页面加载即自动发起 wx.login，尝试静默登录。
- 登录成功直接进入首页。
- 登录失败（未注册）自动跳转注册页。

### 2. 注册页
- 展示：
  - 用户类型选择（必选，单选框/下拉框，严格对应 users.user_type）
  - 手机号输入框（必填，users.phone）
  - 提交按钮
- 提交时自动带上 code，无需用户感知。
- 注册成功后自动登录。

## 四、接口需求（无 api 前缀，字段严格对应 users 表）

### 1. 小程序静默登录接口
- **POST** `/system/auth/miniprogram_login`
- **请求体**：
  - `code: string`  // 微信登录凭证
- **返回**：
  - 已注册：`{ success: true, token, user, ... }`  // user 字段结构与 users 表一致
  - 未注册：`{ success: false, error: '未注册', needRegister: true, openid }`

### 2. 小程序注册接口
- **POST** `/system/auth/miniprogram_register`
- **请求体**：
  - `code: string`      // 微信登录凭证
  - `user_type: string` // 用户类型，严格对应 users.user_type
  - `phone: string`     // 手机号，users.phone
  - （如需密码可加 password，建议后端自动生成或短信验证）
- **返回**：
  - `{ success: true, token, user, ... }`  // user 字段结构与 users 表一致

#### 字段与 users 表映射说明
- `user_id`：后端生成，返回在 user 对象中
- `phone`：注册页必填，唯一
- `user_type`：注册页必选，枚举值（'patient', 'family', 'medical_staff', 'admin'）
- `openid`：后端通过 code 自动获取并绑定，无需前端传递
- 其他字段如 real_name、avatar_url、profile 等可后续补充完善

## 五、伪代码示例

### 1. 静默登录
```js
wx.login({
  success(res) {
    if (res.code) {
      wx.request({
        url: '/system/auth/miniprogram_login',
        method: 'POST',
        data: { code: res.code },
        success(resp) {
          if (resp.data.success) {
            wx.setStorageSync('token', resp.data.token);
            // 进入首页
          } else if (resp.data.needRegister) {
            // 跳转注册页
            wx.navigateTo({ url: '/pages/user-register/user-register' });
          }
        }
      });
    }
  }
});
```

### 2. 注册页提交
```js
// 用户选择类型和输入手机号后
wx.login({
  success(res) {
    if (res.code) {
      wx.request({
        url: '/system/auth/miniprogram_register',
        method: 'POST',
        data: {
          code: res.code,
          user_type: selectedType, // patient/medical_staff 等
          phone: inputPhone
        },
        success(resp) {
          if (resp.data.success) {
            wx.setStorageSync('token', resp.data.token);
            // 进入首页
          }
        }
      });
    }
  }
});
```

## 六、注意事项
- 注册页无需让用户输入/感知 openid，所有 openid 绑定由后端通过 code 自动完成。
- 注册页仅需手机号、用户类型，极简体验。
- 登录/注册接口均需返回 token，便于前端自动登录。
- 后端需保证 code 只能用一次，防止安全隐患。
- 所有用户相关字段严格参考 `databaseDesign.md` 的 `users` 表。

---

如需补充其他业务字段，可在注册接口增加，但核心流程如上。 