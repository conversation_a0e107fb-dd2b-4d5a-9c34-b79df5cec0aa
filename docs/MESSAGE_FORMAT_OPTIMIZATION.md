# 消息中心格式化优化总结

## 优化概述

本次优化对消息中心的显示进行了全面格式化，包括消息类型、订单状态、时间显示等，提升了用户体验和信息可读性。

## 主要优化内容

### 1. 消息类型格式化

#### 类型映射表
| 原始类型 | 格式化显示 | 图标 |
|---------|-----------|------|
| `registration` | 挂号服务 | 🏥 |
| `accompany` | 陪诊服务 | 👥 |
| `hospitalize` | 住院服务 | 🏨 |
| `proxy` | 买药服务 | 💊 |
| `system` | 系统通知 | 🔔 |
| `order` | 订单消息 | 📋 |
| `service` | 服务消息 | 🛎️ |

#### 显示效果
- 添加了类型图标，增强视觉识别
- 中文显示，提升可读性
- 统一的样式设计

### 2. 订单状态格式化

#### 状态映射表
| 原始状态 | 格式化显示 | 样式类 |
|---------|-----------|--------|
| `pending` | 待处理 | 橙色背景 |
| `paid` | 已支付 | 绿色背景 |
| `assigned` | 已分配 | 蓝色背景 |
| `in_progress` | 进行中 | 绿色背景 |
| `completed` | 已完成 | 绿色背景 |
| `cancelled` | 已取消 | 红色背景 |
| `refunded` | 已退款 | 橙色背景 |
| `registered` | 已挂号 | 蓝色背景 |
| `delivered` | 已发货 | 绿色背景 |
| `confirmed` | 已确认 | 绿色背景 |

#### 样式设计
- 不同状态使用不同颜色背景
- 圆角设计，视觉友好
- 字体加粗，突出显示

### 3. 时间格式化

#### 相对时间显示
- **刚刚**: 小于1分钟
- **X分钟前**: 小于1小时
- **X小时前**: 小于24小时
- **X天前**: 小于7天
- **MM-DD HH:mm**: 超过7天显示具体日期

#### 优势
- 更直观的时间感知
- 减少用户认知负担
- 符合现代应用的时间显示习惯

### 4. 消息状态格式化

#### 状态显示
- **未读**: 红色圆点 + "未读"文字
- **已读**: 不显示状态（默认）

#### 视觉设计
- 红色圆点突出未读状态
- 字体加粗，增强可读性

## 技术实现

### 1. 格式化工具函数 (`utils/format.wxs`)

```javascript
// 消息类型格式化
function formatMessageType(type) {
  var typeMap = {
    'registration': '挂号服务',
    'accompany': '陪诊服务', 
    'hospitalize': '住院服务',
    'proxy': '买药服务',
    'system': '系统通知',
    'order': '订单消息',
    'service': '服务消息'
  };
  return typeMap[type] || type || '订单';
}

// 订单状态格式化
function formatOrderStatus(status) {
  var statusMap = {
    'pending': '待处理',
    'paid': '已支付',
    'assigned': '已分配',
    'in_progress': '进行中',
    'completed': '已完成',
    'cancelled': '已取消',
    'refunded': '已退款',
    'registered': '已挂号',
    'delivered': '已发货',
    'confirmed': '已确认'
  };
  return statusMap[status] || status || '未知状态';
}

// 时间格式化
function formatTime(timestamp) {
  // 智能时间显示逻辑
  // 相对时间 + 绝对时间
}
```

### 2. 页面集成

#### WXML模板更新
```xml
<!-- 引入格式化工具 -->
<wxs src="../../utils/format.wxs" module="format" />

<!-- 消息类型显示 -->
<view class="session-type-wrapper">
  <text class="session-type-icon">{{format.getMessageTypeIcon(item.order.serviceType)}}</text>
  <text class="session-type">{{format.formatMessageType(item.order.serviceType)}}</text>
</view>

<!-- 订单状态显示 -->
<text class="order-status status-tag {{format.getStatusClass(item.order.status)}}">
  {{format.formatOrderStatus(item.order.status)}}
</text>

<!-- 时间显示 -->
<text class="session-time">{{format.formatTime(item.createdAt)}}</text>
```

### 3. 样式优化

#### 新增样式类
- `.session-type-wrapper`: 类型图标和文字容器
- `.session-type-icon`: 类型图标样式
- `.status-*`: 各种状态对应的样式类
- 响应式适配样式

## 用户体验提升

### 1. 信息可读性
- 中文显示，降低理解门槛
- 图标辅助，快速识别类型
- 颜色编码，直观区分状态

### 2. 视觉层次
- 清晰的信息层次结构
- 合理的间距和布局
- 统一的视觉风格

### 3. 交互体验
- 智能时间显示
- 状态颜色反馈
- 响应式设计

### 4. 维护性
- 集中管理格式化逻辑
- 易于扩展新的类型和状态
- 代码复用性高

## 文件修改清单

### 核心文件
- `utils/format.wxs` - 新增格式化工具函数
- `pages/message/message.wxml` - 更新模板，使用格式化函数
- `pages/message/message.wxss` - 优化样式，添加新状态样式

### 功能增强
- 消息类型图标显示
- 订单状态颜色编码
- 智能时间格式化
- 响应式设计优化

## 后续优化建议

### 1. 功能扩展
- [ ] 添加消息优先级显示
- [ ] 支持消息分类筛选
- [ ] 添加消息搜索功能
- [ ] 支持消息批量操作

### 2. 交互优化
- [ ] 添加消息预览功能
- [ ] 支持消息置顶
- [ ] 添加消息提醒设置
- [ ] 支持消息转发

### 3. 性能优化
- [ ] 消息分页加载
- [ ] 图片懒加载
- [ ] 消息缓存机制
- [ ] 离线消息同步

## 总结

本次优化通过格式化工具函数，实现了消息中心显示的标准化和用户友好化：

1. **类型识别**: 通过图标和中文名称，快速识别消息类型
2. **状态感知**: 通过颜色编码，直观了解订单状态
3. **时间感知**: 通过相对时间，更好地理解消息时效性
4. **视觉统一**: 通过统一的设计语言，提升整体体验

这些优化显著提升了消息中心的信息可读性和用户体验，为后续功能扩展奠定了良好的基础。 