# 陪诊日记模块设计

## 1. 模块分区

陪诊日记分为两个主视图：
- **我的日记**：仅展示当前登录用户自己创建的日记（可私有/共享/公开）。
- **广场（公开）日记**：展示所有 privacyLevel 为 public 的日记，所有用户可见。

用户可在页面顶部 tab 或切换按钮选择“我的”或“广场”。

---

## 2. 主要功能
- 日记列表（我的/广场）
- 日记详情
- 新建/编辑/删除日记
- 标签筛选、隐私级别筛选
- 支持图片/附件上传
- 可关联服务订单
- 支持自定义标签

---

## 3. API 结构

### 获取我的日记
- `GET /api/social/diaries?userId=<当前用户id>&page=1&pageSize=10`
- 返回：
```json
{
  "success": true,
  "data": {
    "diaries": [ ... ],
    "pagination": { ... }
  }
}
```

### 获取广场日记
- `GET /api/social/diaries?privacyLevel=public&page=1&pageSize=10`
- 返回结构同上。

### 其它
- 创建、编辑、删除、详情接口同现有。

---

## 4. 前端页面结构建议

- **顶部 Tab/切换栏**：我的 | 广场
- **列表区**：卡片式展示日记摘要，显示标题、内容摘要、标签、作者（广场）、关联订单号、时间等。
- **筛选区**：标签筛选、隐私级别筛选（我的页可用）
- **新建按钮**：右下角悬浮或顶部按钮
- **详情页**：展示全部字段，支持图片预览、标签、作者、订单号等

---

## 5. 交互说明
- 默认进入“我的日记”页，tab 可切换到“广场”。
- “我的”页可新建、编辑、删除日记，广场页仅可查看。
- 列表支持下拉刷新、分页加载。
- 筛选支持多标签、隐私级别（我的页）。
- 日记卡片点击进入详情。

---

> 如需扩展评论、点赞、收藏等社交功能，可后续补充。 