# 订单权限修复说明

## 问题描述

病患用户查询到不属于自己的订单，存在数据泄露风险。

## 问题原因分析

### 1. 前端权限验证不完整
- 订单列表页面缺少对病患用户的二次过滤
- 医护人员有权限验证，但病患用户没有

### 2. API接口权限控制缺失
- 后端API可能没有正确验证用户权限
- 前端API调用没有强制传递用户ID参数

### 3. 订单详情页面权限验证不完整
- 只对医护人员进行了权限验证
- 病患用户缺少权限验证

## 修复方案

### 1. 前端订单列表权限验证

**文件：** `pages/orders/orders.js`

**修复内容：**
- 为病患用户添加订单列表过滤逻辑
- 确保病患用户只能查看自己的订单

```javascript
// 权限验证：确保用户只能查看自己的订单
if (userType === 'staff' || userType === 'medical_staff') {
  list = list.filter(item => item.staffId === myStaffId);
} else if (userType === 'patient') {
  // 病患用户只能查看自己的订单
  list = list.filter(item => item.userId === userId);
}
```

### 2. API接口权限验证

**文件：** `api/orders.js`, `api/medicine_orders.js`, `api/registrations.js`

**修复内容：**
- 在API请求前自动添加用户权限参数
- 确保病患用户只能查询自己的订单

```javascript
// 确保病患用户只能查询自己的订单
if (userType === 'patient' && !params.userId) {
  params.userId = userInfo.id;
}
```

### 3. 订单详情页面权限验证

**文件：** `pages/orders/detail.js`

**修复内容：**
- 为病患用户添加订单详情权限验证
- 确保病患用户只能查看自己的订单详情

```javascript
} else if (userType === 'patient') {
  // 病患用户权限校验：只能查看自己的订单
  if (!order.userId || String(order.userId) !== String(userInfo.id)) {
    wx.showToast({ title: '无权查看该订单', icon: 'none' });
    setTimeout(() => wx.navigateBack(), 1500);
    return;
  }
}
```

### 4. 权限验证工具函数

**文件：** `utils/auth.js`

**新增功能：**
- 添加订单权限验证函数
- 添加订单列表过滤函数

```javascript
// 验证订单权限：确保用户只能访问自己的订单
validateOrderPermission(order, userType) {
  const userInfo = this.getUserInfo();
  if (!userInfo) return false;

  if (userType === 'staff' || userType === 'medical_staff') {
    // 医护人员只能查看分配给自己的订单
    return order.staffId === userInfo.staffId;
  } else if (userType === 'patient') {
    // 病患用户只能查看自己的订单
    return order.userId === userInfo.id;
  }
  
  return false;
}
```

## 测试验证

### 1. 病患用户测试
1. 使用病患用户账号登录
2. 进入订单页面
3. 验证只能看到自己的订单
4. 尝试访问其他用户的订单详情，应该被拒绝

### 2. 医护人员测试
1. 使用医护人员账号登录
2. 进入订单页面
3. 验证只能看到分配给自己的订单
4. 尝试访问其他医护人员的订单，应该被拒绝

### 3. 权限边界测试
1. 测试未登录用户访问订单页面
2. 测试用户信息不完整的情况
3. 测试订单数据异常的情况

## 安全建议

### 1. 后端API验证
- 后端API应该始终验证用户权限
- 不要完全依赖前端传递的参数
- 使用JWT token中的用户信息进行验证

### 2. 数据脱敏
- 敏感订单信息应该进行脱敏处理
- 避免在前端显示完整的用户隐私信息

### 3. 日志记录
- 记录所有订单访问日志
- 监控异常访问行为

## 注意事项

1. **向后兼容**：修复不影响现有功能
2. **性能考虑**：前端过滤不会影响性能
3. **用户体验**：权限拒绝时给出友好提示
4. **错误处理**：添加适当的错误处理机制

## 相关文件

- `pages/orders/orders.js` - 订单列表页面
- `pages/orders/detail.js` - 订单详情页面
- `api/orders.js` - 服务订单API
- `api/medicine_orders.js` - 药品订单API
- `api/registrations.js` - 挂号订单API
- `utils/auth.js` - 权限验证工具 