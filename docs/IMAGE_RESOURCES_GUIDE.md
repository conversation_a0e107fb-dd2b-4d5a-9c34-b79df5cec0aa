# 小程序图片资源管理指南

## 问题分析

### 真机上图片不可见的原因

1. **图片文件缺失**
   - 代码中引用了图片，但文件不存在
   - 需要确保所有引用的图片文件都在项目中

2. **图片格式问题**
   - 小程序支持的图片格式：PNG、JPG、JPEG、GIF、WEBP
   - 建议使用PNG或JPG格式，兼容性最好

3. **图片大小限制**
   - 单个图片文件不能超过2MB
   - 建议压缩图片以减小文件大小

4. **路径问题**
   - 使用绝对路径（以/开头）
   - 确保路径正确

## 当前项目中缺失的图片文件

### 需要添加的图片文件

1. **`/assets/default-avatar.png`** - 默认头像
   - 用途：用户没有头像时的默认显示
   - 建议尺寸：200x200px
   - 格式：PNG

2. **`/assets/user-avatar.png`** - 用户头像
   - 用途：聊天界面中的用户头像
   - 建议尺寸：200x200px
   - 格式：PNG

3. **`/assets/empty-notification.png`** - 空通知状态图
   - 用途：消息列表为空时的显示
   - 建议尺寸：300x200px
   - 格式：PNG

## 解决方案

### 1. 添加缺失的图片文件

将以下图片文件添加到 `assets/` 目录：

```
assets/
├── default-avatar.png     # 默认头像
├── user-avatar.png        # 用户头像
├── empty-notification.png # 空通知状态图
├── profile.webp           # 已存在
└── medical-5459653_960_720.webp  # 已存在
```

### 2. 图片优化建议

1. **压缩图片**
   - 使用在线工具压缩图片（如 TinyPNG）
   - 保持图片质量的同时减小文件大小

2. **选择合适的格式**
   - PNG：适合图标、透明背景
   - JPG：适合照片、大图片
   - WEBP：更小的文件大小，但兼容性较差

3. **统一尺寸**
   - 头像类图片：200x200px
   - 状态图：300x200px
   - 背景图：根据实际需要

### 3. 发布时确认图片打包

1. **检查项目配置**
   - 确保 `app.json` 中没有排除图片文件
   - 检查 `.gitignore` 是否排除了图片文件

2. **预览测试**
   - 在微信开发者工具中预览
   - 在真机上测试图片显示

3. **上传代码**
   - 上传代码包时确认图片文件被包含
   - 检查上传后的代码包大小

## 临时解决方案

如果暂时没有合适的图片，可以使用以下方法：

### 1. 使用在线图片（临时）
```xml
<!-- 临时使用在线图片 -->
<image src="https://via.placeholder.com/200x200/cccccc/666666?text=Avatar" />
```

### 2. 使用Base64图片（小图片）
```xml
<!-- 将小图片转换为Base64 -->
<image src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA..." />
```

### 3. 隐藏图片（临时）
```xml
<!-- 临时隐藏图片 -->
<image wx:if="{{false}}" src="/assets/default-avatar.png" />
```

## 最佳实践

1. **统一管理图片资源**
   - 所有图片放在 `assets/` 目录
   - 使用有意义的文件名

2. **图片命名规范**
   - 使用小写字母和连字符
   - 包含尺寸信息（如：avatar-200x200.png）

3. **版本控制**
   - 将图片文件纳入版本控制
   - 避免在代码中引用外部图片

4. **定期检查**
   - 定期检查项目中引用的图片是否存在
   - 删除未使用的图片文件

## 检查清单

- [ ] 所有引用的图片文件都存在
- [ ] 图片格式正确（PNG/JPG）
- [ ] 图片大小合理（<2MB）
- [ ] 图片路径正确（绝对路径）
- [ ] 在真机上测试图片显示
- [ ] 发布前确认图片已打包 