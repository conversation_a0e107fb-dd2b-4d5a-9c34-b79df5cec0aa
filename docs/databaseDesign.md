# PostgreSQL 数据库设计 - 医疗陪伴与事务代办平台 (带字段注释)

以下是每个表的详细字段注释说明：

## 1. 用户表 (users)

```sql
CREATE TABLE users (
    user_id BIGSERIAL PRIMARY KEY,                     -- 用户唯一标识ID，自增
    phone VARCHAR(20) UNIQUE NOT NULL,                -- 用户手机号，唯一且不能为空
    email VARCHAR(255) UNIQUE,                        -- 用户邮箱，唯一
    password_hash VARCHAR(255) NOT NULL,               -- 密码哈希值
    user_type VARCHAR(20) NOT NULL CHECK (user_type IN ('patient', 'family', 'medical_staff', 'admin')), -- 用户类型：患者/家属/医护人员/管理员
    real_name VARCHAR(100),                           -- 真实姓名
    id_card VARCHAR(20),                              -- 身份证号
    avatar_url VARCHAR(255),                          -- 头像URL地址
    profile JSONB,                                    -- 用户详细资料JSON，如性别、生日等
    health_info JSONB,                                -- 健康信息JSON，如过敏史、基础疾病等
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),    -- 创建时间
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),    -- 最后更新时间
    last_login TIMESTAMPTZ                            -- 最后登录时间
);
```

## 2. 医院表 (hospitals)

```sql
CREATE TABLE hospitals (
    hospital_id BIGSERIAL PRIMARY KEY,                -- 医院唯一标识ID
    name VARCHAR(255) NOT NULL,                       -- 医院名称
    level VARCHAR(50),                                -- 医院等级(三甲/二甲等)
    address JSONB NOT NULL,                           -- 地址JSON {province: "", city: "", detail: ""}
    contact_phone VARCHAR(20),                        -- 联系电话
    departments JSONB,                                -- 科室信息JSON {id: name, specialties: [...]}
    facilities JSONB,                                 -- 设施信息JSON
    coordinates POINT,                                -- 地理坐标(经度,纬度)
    rating NUMERIC(3,1),                              -- 医院评分(1-5)
    description TEXT,                                 -- 医院描述
    images TEXT[],                                    -- 医院图片URL数组
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),    -- 创建时间
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()     -- 更新时间
);
```

## 3. 医生/陪诊师表 (medical_staff)

```sql
CREATE TABLE medical_staff (
    staff_id BIGSERIAL PRIMARY KEY,                   -- 医护人员ID
    user_id BIGINT REFERENCES users(user_id) ON DELETE CASCADE, -- 关联用户ID
    hospital_id BIGINT REFERENCES hospitals(hospital_id), -- 所属医院ID
    type VARCHAR(50) NOT NULL CHECK (type IN ('doctor', 'companion', 'nurse', 'other')), -- 人员类型：医生/陪诊师/护士/其他
    qualifications JSONB NOT NULL,                   -- 资质证明JSON {certificates: [{name: url}], license: ...}
    specialties TEXT[],                              -- 专长领域数组
    service_score NUMERIC(3,1),                      -- 服务评分(1-5)
    service_count INT DEFAULT 0,                     -- 服务次数
    introduction TEXT,                               -- 个人介绍
    service_area INT[],                              -- 服务地区ID数组
    service_price JSONB,                             -- 服务价格JSON {standard: 100, overnight: 200}
    is_verified BOOLEAN DEFAULT FALSE,               -- 是否已认证
    available_days JSONB,                            -- 可服务时间JSON {weekdays: [1,2,3], start: "09:00", end: "18:00"}
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),   -- 创建时间
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()    -- 更新时间
);
```

## 4. 服务订单表 (orders)

```sql
CREATE TABLE orders (
    order_id BIGSERIAL PRIMARY KEY,                  -- 订单ID
    order_no VARCHAR(50) UNIQUE NOT NULL,            -- 订单编号(业务号)
    user_id BIGINT REFERENCES users(user_id) ON DELETE SET NULL, -- 用户ID
    staff_id BIGINT REFERENCES medical_staff(staff_id) ON DELETE SET NULL, -- 服务人员ID
    hospital_id BIGINT REFERENCES hospitals(hospital_id), -- 医院ID
    service_type VARCHAR(50) NOT NULL CHECK (service_type IN ('companion', 'proxy', 'hospitalization', 'registration', 'custom')), -- 服务类型
    service_details JSONB NOT NULL,                  -- 服务详情JSON {items: [{type: "挂号", desc: "..."}], requirements: "..."}
    status VARCHAR(20) NOT NULL CHECK (status IN ('pending', 'paid', 'assigned', 'in_progress', 'completed', 'cancelled', 'refunded')), -- 订单状态
    appointment_time TIMESTAMPTZ,                    -- 预约时间
    duration INT,                                    -- 服务时长(分钟)
    price NUMERIC(10,2) NOT NULL,                    -- 订单价格
    payment_info JSONB,                              -- 支付信息JSON
    patient_info JSONB,                              -- 患者信息JSON {name: "", age: 30, condition: "..."}
    location_info JSONB,                             -- 位置信息JSON {address: "", room: ""}
    rating INT CHECK (rating BETWEEN 1 AND 5),       -- 用户评分(1-5)
    review TEXT,                                     -- 用户评价内容
    attachments TEXT[],                              -- 附件URL数组
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),   -- 创建时间
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),   -- 更新时间
    completed_at TIMESTAMPTZ                         -- 完成时间
);
```

## 5. 挂号信息表 (registrations)

```sql
CREATE TABLE registrations (
    registration_id BIGSERIAL PRIMARY KEY,           -- 挂号记录ID
    order_id BIGINT REFERENCES orders(order_id) ON DELETE CASCADE, -- 关联订单ID
    hospital_id BIGINT REFERENCES hospitals(hospital_id), -- 医院ID
    department VARCHAR(100) NOT NULL,               -- 科室名称
    doctor_name VARCHAR(100),                       -- 医生姓名
    visit_date DATE NOT NULL,                        -- 就诊日期
    time_slot VARCHAR(50),                           -- 时间段(如"上午","下午"或具体时间)
    patient_name VARCHAR(100) NOT NULL,             -- 患者姓名
    patient_id_card VARCHAR(20),                     -- 患者身份证号
    symptoms TEXT,                                   -- 症状描述
    status VARCHAR(20) NOT NULL CHECK (status IN ('pending', 'registered', 'completed')), -- 挂号状态
    registration_no VARCHAR(50),                     -- 医院挂号单号
    his_info JSONB,                                  -- 医院HIS系统返回的信息JSON
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),   -- 创建时间
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()    -- 更新时间
);
```

## 6. 陪诊日记表 (diaries)

```sql
CREATE TABLE diaries (
    diary_id BIGSERIAL PRIMARY KEY,                  -- 日记ID
    user_id BIGINT REFERENCES users(user_id) ON DELETE CASCADE, -- 用户ID
    order_id BIGINT REFERENCES orders(order_id) ON DELETE SET NULL, -- 关联订单ID
    title VARCHAR(255),                              -- 日记标题
    content TEXT NOT NULL,                           -- 日记内容
    medical_records JSONB,                           -- 医疗记录JSON {medications: [{name: "", dosage: ""}], tests: []}
    attachments TEXT[],                              -- 附件URL数组(图片/视频等)
    privacy_level VARCHAR(20) NOT NULL DEFAULT 'private' CHECK (privacy_level IN ('private', 'shared', 'public')), -- 隐私级别
    tags TEXT[],                                     -- 标签数组
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),   -- 创建时间
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()    -- 更新时间
);
```

## 7. 评价表 (reviews)

```sql
CREATE TABLE reviews (
    review_id BIGSERIAL PRIMARY KEY,                 -- 评价ID
    order_id BIGINT REFERENCES orders(order_id) ON DELETE CASCADE, -- 关联订单ID
    reviewer_id BIGINT REFERENCES users(user_id) ON DELETE SET NULL, -- 评价人ID
    staff_id BIGINT REFERENCES medical_staff(staff_id) ON DELETE CASCADE, -- 被评价人员ID
    rating INT NOT NULL CHECK (rating BETWEEN 1 AND 5), -- 评分(1-5)
    content TEXT,                                    -- 评价内容
    tags TEXT[],                                     -- 标签数组(如"专业","耐心"等)
    response TEXT,                                   -- 服务人员回复
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),   -- 创建时间
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()    -- 更新时间
);
```

## 8. 消息表 (messages)

```sql
CREATE TABLE messages (
    message_id BIGSERIAL PRIMARY KEY,               -- 消息ID
    sender_id BIGINT REFERENCES users(user_id) ON DELETE SET NULL, -- 发送者ID
    receiver_id BIGINT REFERENCES users(user_id) ON DELETE SET NULL, -- 接收者ID
    order_id BIGINT REFERENCES orders(order_id) ON DELETE SET NULL, -- 关联订单ID
    content TEXT NOT NULL,                          -- 消息内容
    attachments TEXT[],                             -- 附件URL数组
    is_read BOOLEAN DEFAULT FALSE,                 -- 是否已读
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()   -- 创建时间
);
```

## 9. 药品表 (medicines)

```sql
CREATE TABLE medicines (
    medicine_id BIGSERIAL PRIMARY KEY,              -- 药品ID
    name VARCHAR(255) NOT NULL,                    -- 药品名称
    specification VARCHAR(100),                    -- 规格
    manufacturer VARCHAR(255),                      -- 生产厂家
    price NUMERIC(10,2),                           -- 价格
    prescription_required BOOLEAN DEFAULT TRUE,    -- 是否需要处方
    description TEXT,                               -- 药品描述
    image_url VARCHAR(255),                        -- 药品图片URL
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(), -- 创建时间
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()  -- 更新时间
);
```

## 10. 药品订单表 (medicine_orders)

```sql
CREATE TABLE medicine_orders (
    medicine_order_id BIGSERIAL PRIMARY KEY,       -- 药品订单ID
    order_id BIGINT REFERENCES orders(order_id) ON DELETE CASCADE, -- 关联主订单ID
    medicine_id BIGINT REFERENCES medicines(medicine_id), -- 药品ID
    quantity INT NOT NULL,                         -- 购买数量
    prescription_url VARCHAR(255),                 -- 处方图片URL
    status VARCHAR(20) NOT NULL CHECK (status IN ('pending', 'paid', 'preparing', 'shipped', 'delivered', 'completed', 'cancelled', 'refunded')), -- 订单状态
    delivery_info JSONB,                           -- 配送信息JSON
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(), -- 创建时间
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()  -- 更新时间
);
```

这个设计详细注释了每个字段的含义和用途，便于开发人员理解数据库结构。JSONB字段用于存储灵活的结构化数据，数组类型用于存储多值字段，各种约束确保了数据的完整性和一致性。