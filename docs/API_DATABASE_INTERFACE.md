# 数据库表接口文档

本文件根据 `databaseDesign.md` 的表结构，整理出每个表的典型 RESTful 接口，便于前后端协作和接口开发。

---

## 1. 用户表 (users)

### 获取用户列表
- **URL**: `/api/users`
- **Method**: GET
- **Query**: `page`, `pageSize`, `search`
- **说明**: 分页、搜索获取用户列表

### 创建用户
- **URL**: `/api/users`
- **Method**: POST
- **Body**: `phone`, `email`, `password_hash`, `user_type`, `real_name`, `id_card`, `avatar_url`, `profile`, `health_info`
- **说明**: 新增用户

### 获取单个用户详情
- **URL**: `/api/users/:user_id`
- **Method**: GET
- **说明**: 获取指定用户的详细信息

### 更新用户
- **URL**: `/api/users/:user_id`
- **Method**: PUT
- **Body**: 可选字段同上
- **说明**: 更新指定用户信息

### 删除用户
- **URL**: `/api/users/:user_id`
- **Method**: DELETE
- **说明**: 删除指定用户

---

## 2. 医院表 (hospitals)

### 获取医院列表
- **URL**: `/api/hospitals`
- **Method**: GET
- **Query**: `page`, `pageSize`, `search`
- **说明**: 分页、搜索获取医院列表

### 创建医院
- **URL**: `/api/hospitals`
- **Method**: POST
- **Body**: `name`, `level`, `address`, `contact_phone`, `departments`, `facilities`, `coordinates`, `rating`, `description`, `images`
- **说明**: 新增医院

### 获取单个医院详情
- **URL**: `/api/hospitals/:hospital_id`
- **Method**: GET
- **说明**: 获取指定医院的详细信息

### 更新医院
- **URL**: `/api/hospitals/:hospital_id`
- **Method**: PUT
- **Body**: 可选字段同上
- **说明**: 更新指定医院信息

### 删除医院
- **URL**: `/api/hospitals/:hospital_id`
- **Method**: DELETE
- **说明**: 删除指定医院

---

## 3. 医生/陪诊师表 (medical_staff)

### 获取医护人员列表
- **URL**: `/api/medical_staff`
- **Method**: GET
- **Query**: `page`, `pageSize`, `search`, `type`, `hospital_id`
- **说明**: 分页、搜索、类型和医院过滤获取医护人员列表

### 创建医护人员
- **URL**: `/api/medical_staff`
- **Method**: POST
- **Body**: `user_id`, `hospital_id`, `type`, `qualifications`, `specialties`, `service_score`, `service_count`, `introduction`, `service_area`, `service_price`, `is_verified`, `available_days`
- **说明**: 新增医护人员

### 获取单个医护人员详情
- **URL**: `/api/medical_staff/:staff_id`
- **Method**: GET
- **说明**: 获取指定医护人员的详细信息

### 更新医护人员
- **URL**: `/api/medical_staff/:staff_id`
- **Method**: PUT
- **Body**: 可选字段同上
- **说明**: 更新指定医护人员信息

### 删除医护人员
- **URL**: `/api/medical_staff/:staff_id`
- **Method**: DELETE
- **说明**: 删除指定医护人员

---

## 4. 服务订单表 (orders)

### 获取订单列表
- **URL**: `/api/orders`
- **Method**: GET
- **Query**: `page`, `pageSize`, `search`, `status`, `user_id`, `staff_id`, `hospital_id`, `service_type`
- **说明**: 分页、搜索、状态和多条件过滤获取订单列表

### 创建订单
- **URL**: `/api/orders`
- **Method**: POST
- **Body**: `order_no`, `user_id`, `staff_id`, `hospital_id`, `service_type`, `service_details`, `status`, `appointment_time`, `duration`, `price`, `payment_info`, `patient_info`, `location_info`, `attachments`
- **说明**: 新增服务订单

### 获取单个订单详情
- **URL**: `/api/orders/:order_id`
- **Method**: GET
- **说明**: 获取指定订单的详细信息

### 更新订单
- **URL**: `/api/orders/:order_id`
- **Method**: PUT
- **Body**: 可选字段同上
- **说明**: 更新指定订单信息

### 删除订单
- **URL**: `/api/orders/:order_id`
- **Method**: DELETE
- **说明**: 删除指定订单

---

## 5. 挂号信息表 (registrations)

### 获取挂号记录列表
- **URL**: `/api/registrations`
- **Method**: GET
- **Query**: `page`, `pageSize`, `search`, `hospital_id`, `department`, `visit_date`, `patient_name`, `status`
- **说明**: 分页、搜索和多条件过滤获取挂号记录

### 创建挂号记录
- **URL**: `/api/registrations`
- **Method**: POST
- **Body**: `order_id`, `hospital_id`, `department`, `doctor_name`, `visit_date`, `time_slot`, `patient_name`, `patient_id_card`, `symptoms`, `status`, `registration_no`, `his_info`
- **说明**: 新增挂号记录

### 获取单个挂号详情
- **URL**: `/api/registrations/:registration_id`
- **Method**: GET
- **说明**: 获取指定挂号记录的详细信息

### 更新挂号记录
- **URL**: `/api/registrations/:registration_id`
- **Method**: PUT
- **Body**: 可选字段同上
- **说明**: 更新指定挂号记录信息

### 删除挂号记录
- **URL**: `/api/registrations/:registration_id`
- **Method**: DELETE
- **说明**: 删除指定挂号记录

---

## 6. 陪诊日记表 (diaries)

### 获取日记列表
- **URL**: `/api/diaries`
- **Method**: GET
- **Query**: `page`, `pageSize`, `search`, `user_id`, `order_id`, `privacy_level`, `tags`
- **说明**: 分页、搜索和多条件过滤获取日记

### 创建日记
- **URL**: `/api/diaries`
- **Method**: POST
- **Body**: `user_id`, `order_id`, `title`, `content`, `medical_records`, `attachments`, `privacy_level`, `tags`
- **说明**: 新增陪诊日记

### 获取单个日记详情
- **URL**: `/api/diaries/:diary_id`
- **Method**: GET
- **说明**: 获取指定日记的详细信息

### 更新日记
- **URL**: `/api/diaries/:diary_id`
- **Method**: PUT
- **Body**: 可选字段同上
- **说明**: 更新指定日记信息

### 删除日记
- **URL**: `/api/diaries/:diary_id`
- **Method**: DELETE
- **说明**: 删除指定日记

---

## 7. 评价表 (reviews)

### 获取评价列表
- **URL**: `/api/reviews`
- **Method**: GET
- **Query**: `page`, `pageSize`, `search`, `order_id`, `reviewer_id`, `staff_id`, `tags`
- **说明**: 分页、搜索和多条件过滤获取评价

### 创建评价
- **URL**: `/api/reviews`
- **Method**: POST
- **Body**: `order_id`, `reviewer_id`, `staff_id`, `rating`, `content`, `tags`, `response`
- **说明**: 新增评价

### 获取单个评价详情
- **URL**: `/api/reviews/:review_id`
- **Method**: GET
- **说明**: 获取指定评价的详细信息

### 更新评价
- **URL**: `/api/reviews/:review_id`
- **Method**: PUT
- **Body**: 可选字段同上
- **说明**: 更新指定评价信息

### 删除评价
- **URL**: `/api/reviews/:review_id`
- **Method**: DELETE
- **说明**: 删除指定评价

---

## 8. 消息表 (messages)

### 获取消息列表
- **URL**: `/api/messages`
- **Method**: GET
- **Query**: `page`, `pageSize`, `search`, `sender_id`, `receiver_id`, `order_id`, `is_read`
- **说明**: 分页、搜索和多条件过滤获取消息

### 创建消息
- **URL**: `/api/messages`
- **Method**: POST
- **Body**: `sender_id`, `receiver_id`, `order_id`, `content`, `attachments`, `is_read`
- **说明**: 新增消息

### 获取单个消息详情
- **URL**: `/api/messages/:message_id`
- **Method**: GET
- **说明**: 获取指定消息的详细信息

### 更新消息
- **URL**: `/api/messages/:message_id`
- **Method**: PUT
- **Body**: 可选字段同上
- **说明**: 更新指定消息信息

### 删除消息
- **URL**: `/api/messages/:message_id`
- **Method**: DELETE
- **说明**: 删除指定消息

---

## 9. 药品表 (medicines)

### 获取药品列表
- **URL**: `/api/medicines`
- **Method**: GET
- **Query**: `page`, `pageSize`, `search`, `prescription_required`
- **说明**: 分页、搜索和处方要求过滤获取药品

### 创建药品
- **URL**: `/api/medicines`
- **Method**: POST
- **Body**: `name`, `specification`, `manufacturer`, `price`, `prescription_required`, `description`, `image_url`
- **说明**: 新增药品

### 获取单个药品详情
- **URL**: `/api/medicines/:medicine_id`
- **Method**: GET
- **说明**: 获取指定药品的详细信息

### 更新药品
- **URL**: `/api/medicines/:medicine_id`
- **Method**: PUT
- **Body**: 可选字段同上
- **说明**: 更新指定药品信息

### 删除药品
- **URL**: `/api/medicines/:medicine_id`
- **Method**: DELETE
- **说明**: 删除指定药品

---

## 10. 药品订单表 (medicine_orders)

### 获取药品订单列表
- **URL**: `/api/medicine_orders`
- **Method**: GET
- **Query**: `page`, `pageSize`, `search`, `order_id`, `medicine_id`, `status`
- **说明**: 分页、搜索和多条件过滤获取药品订单

### 创建药品订单
- **URL**: `/api/medicine_orders`
- **Method**: POST
- **Body**: `order_id`, `medicine_id`, `quantity`, `prescription_url`, `status`, `delivery_info`
- **说明**: 新增药品订单

### 获取单个药品订单详情
- **URL**: `/api/medicine_orders/:medicine_order_id`
- **Method**: GET
- **说明**: 获取指定药品订单的详细信息

### 更新药品订单
- **URL**: `/api/medicine_orders/:medicine_order_id`
- **Method**: PUT
- **Body**: 可选字段同上
- **说明**: 更新指定药品订单信息

### 删除药品订单
- **URL**: `/api/medicine_orders/:medicine_order_id`
- **Method**: DELETE
- **说明**: 删除指定药品订单

---

## 服务订单（service_orders）API 返回数据结构

### 示例

```
{
  "success": true,
  "data": {
    "orders": [
      {
        "id": 213,
        "orderNo": "SRV20250714113641EDMEO9", // 订单号
        "userId": 22, // 下单用户ID
        "staffId": 1, // 负责医护ID
        "hospitalId": 25, // 医院ID
        "serviceType": "proxy", // 服务类型（如 proxy/registration/medicine 等）
        "serviceDetails": {
          "items": [
            {
              "desc": "Quia ut qui aut quos et.", // 服务/药品描述
              "name": "Unbranded Steel Tuna片", // 名称
              "type": "medicine", // 类型
              "price": 18, // 单价
              "quantity": 1 // 数量
            }
          ]
        },
        "status": "completed", // 订单状态
        "appointmentTime": null, // 预约时间（如有）
        "duration": null, // 服务时长（如有）
        "price": "18.00", // 总价
        "paymentInfo": null, // 支付信息（如有）
        "patientInfo": null, // 患者信息（如有）
        "locationInfo": null, // 地点信息（如有）
        "rating": null, // 评分（如有）
        "review": null, // 评价（如有）
        "attachments": null, // 附件（如有）
        "completedAt": "2025-07-14T06:10:49.692Z", // 完成时间
        "createdBy": 22, // 创建人ID
        "updatedBy": 21, // 最后更新人ID
        "createdAt": "2025-07-14T03:36:41.803Z", // 创建时间
        "updatedAt": "2025-07-14T06:10:49.693Z", // 更新时间
        "deletedAt": null, // 删除时间（如有）
        "user": { // 下单用户信息
          "id": 22,
          "username": "陈清扬",
          "mobile": "18800000002"
        },
        "staff": { // 负责医护信息
          "id": 1,
          "type": "companion",
          "serviceScore": "5.0",
          "user": {
            "id": 21,
            "username": "王二",
            "mobile": "18800000001"
          }
        },
        "hospital": { // 医院信息
          "id": 25,
          "name": "北京儿童医院",
          "level": "三甲",
          "address": {
            "city": "",
            "detail": "96593 Clay Underpass",
            "province": ""
          }
        },
        "creator": { // 创建人信息
          "id": 22,
          "username": "陈清扬"
        }
      }
      // ... 其他订单 ...
    ],
    "pagination": {
      "total": 213, // 总条数
      "page": 1, // 当前页
      "limit": 10, // 每页数量
      "totalPages": 22 // 总页数
    }
  }
}
```

### 字段说明
- `orders[]`：服务订单列表，每项为一个订单对象，字段如上。
- `pagination`：分页信息。
- 订单对象内嵌 `user`、`staff`、`hospital`、`creator` 等详细信息，便于前端直接展示。
- `serviceDetails.items[]`：服务明细，支持多项（如药品、挂号等）。
- 其他字段如 `status`、`price`、`createdAt`、`updatedAt` 等均为常规业务字段。

> 注：如需补充字段或有结构调整，请与后端及时沟通并同步文档。 