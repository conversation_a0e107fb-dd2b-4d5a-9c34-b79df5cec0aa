

### 小程序整体架构

**核心模块**：

1. 用户服务模块（个人中心）
2. 医疗服务发现模块
3. 服务预约与订单模块
4. 医疗日记与健康档案
5. 消息与通知中心

------

### 小程序页面结构设计

#### 1. 用户服务模块（个人中心）

| 页面名称        | 功能描述         | 关联数据库表          | 核心功能点                                             |
| --------------- | ---------------- | --------------------- | ------------------------------------------------------ |
| **登录/注册页** | 用户身份认证     | `users`               | 手机号验证码登录、用户类型选择                         |
| **个人资料页**  | 管理用户信息     | `users`               | 完善个人资料、健康信息(health_info)、实名认证(id_card) |
| **我的订单页**  | 订单统一管理     | `orders`              | 按状态筛选(pending/paid/completed等)、订单详情查看     |
| **我的地址簿**  | 常用就诊地址管理 | `users.profile`       | 添加医院地址、家庭地址、默认就诊地                     |
| **支付管理**    | 支付账户设置     | `orders.payment_info` | 绑定支付方式、查看支付记录                             |

#### 2. 医疗服务发现模块

| 页面名称           | 功能描述            | 关联数据库表    | 核心功能点                                                   |
| ------------------ | ------------------- | --------------- | ------------------------------------------------------------ |
| **医院发现页**     | 医院搜索与筛选      | `hospitals`     | 按等级/距离/科室筛选、地图定位(coordinates)、号源查询        |
| **医院详情页**     | 医院详细信息展示    | `hospitals`     | 科室列表(departments)、设施信息(facilities)、用户评价(rating) |
| **服务人员发现页** | 陪诊师/医护人员搜索 | `medical_staff` | 按资质/专长/评分筛选、服务价格(service_price)展示            |
| **服务人员详情页** | 人员详细信息        | `medical_staff` | 资质证明(qualifications)、服务时间(available_days)、用户评价(reviews) |
| **药品商城页**     | 药品查询与购买      | `medicines`     | 处方药/非处方药筛选、药品详情(description)、在线开方         |

#### 3. 服务预约与订单模块

| 页面名称       | 功能描述       | 关联数据库表    | 核心功能点                                                   |
| -------------- | -------------- | --------------- | ------------------------------------------------------------ |
| **服务分类页** | 服务类型选择   | -               | 五大服务入口：陪诊、代办、住院、挂号、买药                   |
| **智能预约页** | 服务定制预约   | `orders`        | 发布需求(service_details)、智能匹配服务人员                  |
| **挂号预约页** | 医院挂号功能   | `registrations` | 选择科室/医生/时间(time_slot)、填写症状(symptoms)、在线支付  |
| **陪诊服务页** | 陪诊服务定制   | `orders`        | 选择服务项目(挂号/取药/陪同等)、陪诊师匹配、预约时间(appointment_time) |
| **住院服务页** | 住院陪护服务   | `orders`        | 选择陪护天数(duration)、特殊需求(service_details)、签订服务协议 |
| **订单详情页** | 订单全流程跟踪 | `orders`        | 状态实时更新(status)、服务人员位置、在线沟通(messages)       |
| **评价页**     | 服务评价       | `reviews`       | 星级评分(rating)、标签评价(tags)、服务人员回复(response)     |

#### 4. 医疗日记与健康档案

| 页面名称       | 功能描述     | 关联数据库表              | 核心功能点                                                   |
| -------------- | ------------ | ------------------------- | ------------------------------------------------------------ |
| **日记列表页** | 个人健康记录 | `diaries`                 | 按时间/标签(tags)筛选日记、隐私设置(privacy_level)           |
| **日记编辑页** | 创建健康日记 | `diaries`                 | 图文编辑(content)、医疗记录(medical_records)、附件上传(attachments) |
| **健康档案页** | 健康数据管理 | `diaries.medical_records` | 结构化健康数据展示（用药记录、检查报告等）                   |
| **报告解读页** | 医疗报告管理 | `diaries.attachments`     | 检查报告上传、在线解读申请                                   |

#### 5. 消息与通知中心

| 页面名称       | 功能描述 | 关联数据库表 | 核心功能点                                          |
| -------------- | -------- | ------------ | --------------------------------------------------- |
| **消息中心页** | 沟通管理 | `messages`   | 会话列表、服务人员沟通、系统通知                    |
| **聊天页**     | 实时沟通 | `messages`   | 文字/图片/附件发送(attachments)、订单关联(order_id) |
| **系统通知页** | 平台通知 | -            | 订单状态变更、服务提醒、系统公告                    |

------

### 核心功能流程设计

#### 1. 陪诊服务预约流程：

```
用户 → 选择陪诊服务 → 设置服务详情(service_details) → 选择陪诊师(staff_id) 
→ 确认预约时间(appointment_time) → 支付订单(price) → 创建订单(orders)
→ 服务过程中消息沟通(messages) → 服务完成评价(reviews)
```

#### 2. 代办挂号流程：

```
用户 → 选择代办挂号 → 选择医院(hospital_id)/科室(department) 
→ 填写患者信息(patient_info) → 支付挂号费 → 创建挂号记录(registrations)
→ 代办人员完成挂号 → 同步挂号状态(status)/挂号单号(registration_no)
```

#### 3. 健康日记创建流程：

```
用户 → 新建日记 → 编辑内容(content) → 添加医疗记录(medical_records)
→ 上传检查报告(attachments) → 设置隐私级别(privacy_level)
→ 保存至健康档案(diaries)
```



------

### 页面原型示意图

```
[首页] → 顶部搜索栏 + 服务分类入口(5大服务)
       ↓
[医院列表] → 地图模式/列表模式
       ↓
[服务人员列表] → 卡片式展示(头像/评分/专长)
       ↓
[订单中心] → 状态分类标签 + 时间轴展示
       ↓
[日记列表] → 日历视图 + 卡片式日记
```