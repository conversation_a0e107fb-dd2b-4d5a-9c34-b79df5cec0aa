# 全局布局设计

## 导航栏设计
- 使用固定定位的全局导航栏组件
- 导航栏高度：`88rpx`（min-height）
- 页面顶部边距与导航栏高度保持一致：`88rpx`

## 页面布局原则
- 页面内容从导航栏下方开始
- 顶部边距 = 导航栏高度，避免重叠
- 使用 `padding-top: env(safe-area-inset-top)` 适配刘海屏

## 实现方式
```wxml
<!-- 全局导航栏 -->
<global-navbar title="页面标题" />

<!-- 页面容器 -->
<view class="page-container">
  <!-- 顶部边距，与导航栏高度相等 -->
  <view style="height: 88rpx; padding-top: env(safe-area-inset-top);"></view>
  
  <!-- 页面内容 -->
  <view class="content">
    <!-- 具体内容 -->
  </view>
</view>
```

## 优势
- 简单直接，无需复杂计算
- 统一布局，便于维护
- 适配不同设备 