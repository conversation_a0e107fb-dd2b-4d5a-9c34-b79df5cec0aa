# 小程序图片打包检查指南

## 重要说明

**不需要在 `app.json` 中引用图片文件！**

小程序的打包机制会自动包含项目目录中的所有文件，包括图片资源。

## 打包机制说明

### 1. **自动包含规则**
- 小程序打包时会自动扫描项目目录
- 所有在代码中引用的文件都会被包含
- 图片文件不需要在 `app.json` 中声明

### 2. **项目配置检查**

你的 `project.config.json` 配置正确：
```json
{
  "packOptions": {
    "ignore": [],
    "include": []
  }
}
```

- `ignore: []` - 没有忽略任何文件
- `include: []` - 没有特殊包含规则，使用默认规则

## 图片打包确认方法

### 1. **检查图片文件是否存在**

确保以下图片文件存在于 `assets/` 目录：

```
assets/
├── default-avatar.png     # 默认头像
├── user-avatar.png        # 用户头像  
├── empty-notification.png # 空通知状态图
├── profile.webp           # 已存在
└── medical-5459653_960_720.webp  # 已存在
```

### 2. **检查代码中的引用**

确保代码中正确引用了图片：

```xml
<!-- 正确的引用方式 -->
<image src="/assets/default-avatar.png" />

<!-- 错误的引用方式 -->
<image src="assets/default-avatar.png" />  <!-- 缺少前导斜杠 -->
<image src="./assets/default-avatar.png" /> <!-- 相对路径 -->
```

### 3. **预览和真机测试**

1. **开发者工具预览**
   - 在微信开发者工具中点击"预览"
   - 生成二维码，用微信扫码测试

2. **真机调试**
   - 在微信开发者工具中点击"真机调试"
   - 在真机上查看图片是否正常显示

3. **上传代码**
   - 点击"上传"按钮
   - 检查上传后的代码包大小

### 4. **检查代码包大小**

上传后检查代码包大小：
- 如果图片文件较大，会影响代码包大小
- 建议压缩图片以减小文件大小

## 常见问题解决

### 1. **图片在模拟器可见，真机不可见**

**可能原因：**
- 图片文件不存在
- 图片格式不支持
- 图片路径错误

**解决方案：**
```javascript
// 使用图片工具类处理
const { getSafeImagePath } = require('../../utils/imageUtils.js');

// 在页面中使用
Page({
  data: {
    avatarUrl: ''
  },
  
  onLoad() {
    // 安全获取图片路径
    getSafeImagePath('/assets/default-avatar.png', 'DEFAULT_AVATAR')
      .then(safeUrl => {
        this.setData({ avatarUrl: safeUrl });
      });
  }
});
```

### 2. **图片加载失败**

**解决方案：**
```xml
<!-- 添加错误处理 -->
<image 
  src="{{avatarUrl}}" 
  binderror="onImageError"
  mode="aspectFill" 
/>

<!-- 添加加载状态 -->
<image 
  src="{{avatarUrl}}" 
  bindload="onImageLoad"
  binderror="onImageError"
  mode="aspectFill" 
/>
```

```javascript
Page({
  onImageError(e) {
    console.error('图片加载失败:', e.detail);
    // 使用默认图片
    this.setData({
      avatarUrl: 'https://via.placeholder.com/200x200/cccccc/666666?text=Avatar'
    });
  },
  
  onImageLoad(e) {
    console.log('图片加载成功');
  }
});
```

### 3. **图片文件过大**

**解决方案：**
1. 压缩图片（使用 TinyPNG 等工具）
2. 选择合适的格式（PNG/JPG）
3. 调整图片尺寸

## 发布前检查清单

- [ ] 所有引用的图片文件都存在
- [ ] 图片格式正确（PNG/JPG/WEBP）
- [ ] 图片大小合理（<2MB）
- [ ] 图片路径正确（绝对路径）
- [ ] 在真机上测试图片显示
- [ ] 检查代码包大小
- [ ] 上传代码并确认

## 最佳实践

1. **统一管理图片资源**
   - 所有图片放在 `assets/` 目录
   - 使用有意义的文件名

2. **图片优化**
   - 压缩图片减小文件大小
   - 选择合适的格式和尺寸

3. **错误处理**
   - 添加图片加载错误处理
   - 提供默认图片作为备选

4. **测试验证**
   - 在模拟器和真机上都测试
   - 确保图片正常显示 