# 全局导航栏使用说明

## 概述

本项目已配置全局导航栏组件，提供统一的页面导航体验，包含返回按钮和页面标题。

## 组件特性

- ✅ 自动适配状态栏高度
- ✅ 支持自定义标题和返回按钮
- ✅ 支持自定义颜色主题
- ✅ 支持右侧插槽内容
- ✅ 深色主题适配
- ✅ 响应式设计

## 使用方法

### 1. 基础使用

在页面的 WXML 文件中添加：

```xml
<!-- 全局导航栏 -->
<global-navbar 
  title="页面标题" 
  show-back="{{true}}"
  text-color="#000000"
  background-color="#ffffff"
/>

<!-- 页面内容 -->
<view class="page-content" style="padding-top: {{navbarHeight}}px;">
  <!-- 你的页面内容 -->
</view>
```

### 2. 在页面 JS 中使用导航栏行为

```javascript
import navbarBehavior from '../../behaviors/navbar';

Page({
  behaviors: [navbarBehavior],
  
  data: {
    pageTitle: '页面标题'
  },
  
  onLoad() {
    // 设置页面标题
    this.setPageTitle(this.data.pageTitle);
  }
});
```

### 3. 页面配置

在页面的 `json` 文件中设置：

```json
{
  "navigationStyle": "custom",
  "usingComponents": {
    "t-icon": "tdesign-miniprogram/icon/icon"
  }
}
```

## 组件属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| title | String | '' | 页面标题 |
| showBack | Boolean | true | 是否显示返回按钮 |
| textColor | String | '#000000' | 文字颜色 |
| backgroundColor | String | '#ffffff' | 背景颜色 |
| fixed | Boolean | true | 是否固定在顶部 |

## 插槽

### right 插槽

用于在导航栏右侧添加自定义内容：

```xml
<global-navbar title="页面标题">
  <view slot="right">
    <t-icon name="more" size="24" bindtap="showMore" />
  </view>
</global-navbar>
```

## 行为方法

通过 `navbarBehavior` 可以使用以下方法：

### setPageTitle(title)

设置页面标题：

```javascript
this.setPageTitle('新的页面标题');
```

### goBack()

返回上一页：

```javascript
this.goBack();
```

### goHome()

返回首页：

```javascript
this.goHome();
```

## 样式定制

### 自定义样式

可以通过 CSS 变量自定义样式：

```css
.global-navbar {
  --navbar-bg-color: #your-color;
  --navbar-text-color: #your-color;
}
```

### 响应式适配

组件会自动适配不同设备的状态栏高度，无需手动设置。

## 最佳实践

### 1. 首页不显示返回按钮

```xml
<global-navbar 
  title="首页" 
  show-back="{{false}}"
/>
```

### 2. 透明背景导航栏

```xml
<global-navbar 
  title="页面标题" 
  background-color="transparent"
  text-color="#ffffff"
/>
```

### 3. 动态设置标题

```javascript
Page({
  behaviors: [navbarBehavior],
  
  onLoad(options) {
    // 根据参数动态设置标题
    if (options.id) {
      this.setPageTitle('详情页面');
    } else {
      this.setPageTitle('列表页面');
    }
  }
});
```

## 注意事项

1. 使用全局导航栏时，页面配置中需要设置 `"navigationStyle": "custom"`
2. 页面内容需要设置 `padding-top: {{navbarHeight}}px` 来避免被导航栏遮挡
3. 导航栏会自动处理返回逻辑，无需手动实现
4. 组件已注册为全局组件，无需在每个页面单独引入

## 工具函数

项目还提供了导航栏工具函数，位于 `utils/navbar.js`：

```javascript
const navbarUtils = require('../../utils/navbar');

// 获取导航栏高度
const { statusBarHeight, navbarHeight } = navbarUtils.getNavbarHeight();

// 设置页面标题
navbarUtils.setPageTitle('页面标题');

// 返回上一页
navbarUtils.goBack();

// 返回首页
navbarUtils.goHome();

// 使用预设配置
const config = navbarUtils.createNavbarConfig('transparent', {
  title: '自定义标题'
});
```

### 预设配置

- `default`: 默认导航栏（白色背景，黑色文字）
- `transparent`: 透明导航栏（透明背景，白色文字）
- `home`: 首页导航栏（不显示返回按钮）
- `dark`: 深色导航栏（深色背景，白色文字）

## 示例页面

参考 `pages/example/example` 页面查看完整的使用示例。 