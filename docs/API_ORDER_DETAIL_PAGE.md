# 订单详情页设计文档

## 概述

本文档详细说明订单详情页的设计和实现，特别关注药品订单（`serviceType: 'proxy'`）的特殊显示逻辑。

## 1. 订单详情页基础结构

### 1.1 页面布局
```
┌─────────────────────────────────────┐
│ 全局导航栏（返回 + 订单详情）        │
├─────────────────────────────────────┤
│ 订单状态卡片                        │
│ ┌─────────────────────────────────┐ │
│ │ 订单状态 + 状态描述             │ │
│ └─────────────────────────────────┘ │
├─────────────────────────────────────┤
│ 基本信息卡片                        │
│ ┌─────────────────────────────────┐ │
│ │ 订单号、创建时间、服务类型等     │ │
│ └─────────────────────────────────┘ │
├─────────────────────────────────────┤
│ 服务信息卡片                        │
│ ┌─────────────────────────────────┐ │
│ │ 医院、医护人员、患者信息等       │ │
│ └─────────────────────────────────┘ │
├─────────────────────────────────────┤
│ [药品订单专用] 药品信息卡片          │
│ ┌─────────────────────────────────┐ │
│ │ 药品列表、数量、价格等           │ │
│ └─────────────────────────────────┘ │
├─────────────────────────────────────┤
│ [药品订单专用] 配送信息卡片          │
│ ┌─────────────────────────────────┐ │
│ │ 配送地址、配送状态等             │ │
│ └─────────────────────────────────┘ │
├─────────────────────────────────────┤
│ 费用信息卡片                        │
│ ┌─────────────────────────────────┐ │
│ │ 总价、支付状态等                 │ │
│ └─────────────────────────────────┘ │
├─────────────────────────────────────┤
│ 操作按钮区域                        │
│ ┌─────────────────────────────────┐ │
│ │ 去支付、取消、完成等操作按钮     │ │
│ └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

## 2. 药品订单特殊显示逻辑

### 2.1 药品订单识别
```javascript
// 判断是否为药品订单
const isMedicineOrder = order.serviceType === 'proxy';
```

### 2.2 药品信息卡片（仅药品订单显示）

#### 2.2.1 卡片标题
```
💊 药品信息
```

#### 2.2.2 卡片内容
```javascript
// 从 serviceDetails.items 中获取药品信息
const medicineItems = order.serviceDetails?.items || [];

// 显示格式
medicineItems.map(item => ({
  name: item.name,           // 药品名称
  specification: item.desc,  // 规格描述
  price: item.price,         // 单价
  quantity: item.quantity,   // 数量
  totalPrice: item.price * item.quantity  // 小计
}));
```

#### 2.2.3 显示字段
- **药品名称**：`item.name`
- **规格**：`item.desc`
- **单价**：`¥${item.price}`
- **数量**：`x${item.quantity}`
- **小计**：`¥${item.price * item.quantity}`

#### 2.2.4 样式要求
- 每个药品项使用列表形式显示
- 价格使用红色突出显示
- 数量使用灰色显示
- 小计右对齐显示

### 2.3 配送信息卡片（仅药品订单显示）

#### 2.3.1 卡片标题
```
🚚 配送信息
```

#### 2.3.2 卡片内容
```javascript
// 从 deliveryInfo 中获取配送信息
const deliveryInfo = order.deliveryInfo || {};

// 显示字段
{
  address: deliveryInfo.address,        // 配送地址
  status: getDeliveryStatus(order),     // 配送状态
  trackingInfo: getTrackingInfo(order)  // 配送跟踪信息
}
```

#### 2.3.3 配送状态映射
```javascript
function getDeliveryStatus(order) {
  const medicineOrder = order.medicineOrder; // 关联的药品子订单
  
  if (!medicineOrder) return '待处理';
  
  switch (medicineOrder.status) {
    case 'pending': return '待处理';
    case 'preparing': return '配药中';
    case 'shipped': return '已发货';
    case 'delivered': return '已送达';
    case 'completed': return '已完成';
    case 'cancelled': return '已取消';
    default: return '未知状态';
  }
}
```

#### 2.3.4 显示字段
- **配送地址**：`deliveryInfo.address`
- **配送状态**：根据药品子订单状态显示
- **配送时间**：如果有配送时间信息则显示
- **配送员信息**：如果有配送员信息则显示

### 2.4 处方信息显示（仅药品订单显示）

#### 2.4.1 处方图片
```javascript
// 从 prescriptionUrl 中获取处方图片
const prescriptionUrl = order.prescriptionUrl;

// 显示逻辑
if (prescriptionUrl) {
  // 显示处方图片
  // 支持点击预览
} else {
  // 显示"无处方"或隐藏该区域
}
```

#### 2.4.2 处方要求提示
```javascript
// 检查是否有需要处方的药品
const hasPrescriptionRequired = medicineItems.some(item => 
  item.prescriptionRequired
);

if (hasPrescriptionRequired && !prescriptionUrl) {
  // 显示"需要处方"提示
}
```

## 3. 订单状态显示

### 3.1 主服务订单状态
```javascript
const mainOrderStatus = order.status;
const statusText = {
  'pending': '待处理',
  'paid': '已支付',
  'assigned': '已分配',
  'in_progress': '进行中',
  'completed': '已完成',
  'cancelled': '已取消',
  'refunded': '已退款'
};
```

### 3.2 药品子订单状态（仅药品订单）
```javascript
const medicineOrderStatus = order.medicineOrder?.status;
const medicineStatusText = {
  'pending': '待处理',
  'preparing': '配药中',
  'shipped': '已发货',
  'delivered': '已送达',
  'completed': '已完成',
  'cancelled': '已取消'
};
```

### 3.3 状态显示逻辑
```javascript
// 药品订单的状态显示
if (isMedicineOrder) {
  // 显示主服务订单状态 + 药品配送状态
  const displayStatus = `${mainOrderStatusText} · ${medicineStatusText}`;
} else {
  // 仅显示主服务订单状态
  const displayStatus = mainOrderStatusText;
}
```

## 4. 操作按钮逻辑

### 4.1 药品订单操作按钮
```javascript
function getMedicineOrderButtons(order, userRole) {
  const mainStatus = order.status;
  const medicineStatus = order.medicineOrder?.status;
  
  // 主服务订单状态决定操作权限
  if (mainStatus !== 'in_progress') {
    // 主服务订单未进行中，不显示药品相关操作
    return getMainOrderButtons(order, userRole);
  }
  
  // 药品子订单状态决定具体操作
  if (userRole === 'patient') {
    // 病患端操作
    switch (medicineStatus) {
      case 'pending':
      case 'preparing':
        return ['取消订单', '申请退款'];
      case 'shipped':
        return ['确认收货', '申请退款'];
      case 'delivered':
        return ['申请售后'];
      case 'completed':
        return ['申请售后'];
      default:
        return getMainOrderButtons(order, userRole);
    }
  } else if (userRole === 'staff' || userRole === 'medical_staff') {
    // staff端操作
    switch (medicineStatus) {
      case 'pending':
      case 'paid':
        return ['备货'];
      case 'preparing':
        return ['发货'];
      case 'shipped':
      case 'delivered':
        return []; // 不显示任何操作
      default:
        return getMainOrderButtons(order, userRole);
    }
  }
}
```

### 4.2 主服务订单操作按钮
```javascript
function getMainOrderButtons(order, userRole) {
  const status = order.status;
  
  if (userRole === 'patient') {
    switch (status) {
      case 'pending':
        return ['去支付', '取消订单'];
      case 'paid':
        return ['申请退款', '取消订单'];
      case 'assigned':
        return ['申请退款', '取消订单'];
      case 'in_progress':
        return ['完成服务', '申请退款', '取消订单'];
      case 'completed':
        return ['申请售后'];
      default:
        return [];
    }
  } else if (userRole === 'staff' || userRole === 'medical_staff') {
    switch (status) {
      case 'pending':
        return ['分配'];
      case 'paid':
        return ['分配'];
      case 'assigned':
        return ['开始服务'];
      case 'in_progress':
        return []; // staff不再显示完成服务按钮
      case 'completed':
        return [];
      default:
        return [];
    }
  }
}
```

## 5. 数据获取逻辑

### 5.1 订单详情API
```javascript
// 获取订单详情
const orderDetail = await getOrderDetail(orderId);

// 如果是药品订单，需要额外获取药品子订单信息
if (orderDetail.serviceType === 'proxy') {
  const medicineOrder = await getMedicineOrderByServiceOrder(orderId);
  orderDetail.medicineOrder = medicineOrder;
}
```

### 5.2 药品子订单API
```javascript
// 根据服务订单ID获取药品子订单
function getMedicineOrderByServiceOrder(serviceOrderId) {
  return new Promise((resolve, reject) => {
    wx.request({
      url: `${API_BASE}/orders/medicine_orders/by_service_order/${serviceOrderId}`,
      method: 'GET',
      success: (res) => resolve(res.data),
      fail: reject
    });
  });
}
```

## 6. 页面实现示例

### 6.1 WXML结构
```xml
<!-- 订单状态卡片 -->
<view class="order-status-card">
  <view class="status-title">{{orderStatusText}}</view>
  <view class="status-desc">{{orderStatusDesc}}</view>
</view>

<!-- 基本信息卡片 -->
<view class="order-info-card">
  <view class="card-title">基本信息</view>
  <view class="info-item">
    <text class="label">订单号：</text>
    <text class="value">{{order.orderNo}}</text>
  </view>
  <view class="info-item">
    <text class="label">创建时间：</text>
    <text class="value">{{order.createdAt}}</text>
  </view>
  <view class="info-item">
    <text class="label">服务类型：</text>
    <text class="value">{{serviceTypeText}}</text>
  </view>
</view>

<!-- 服务信息卡片 -->
<view class="service-info-card">
  <view class="card-title">服务信息</view>
  <view class="info-item">
    <text class="label">医院：</text>
    <text class="value">{{order.hospital.name}}</text>
  </view>
  <view class="info-item">
    <text class="label">医护人员：</text>
    <text class="value">{{order.staff.user.username}}</text>
  </view>
  <view class="info-item">
    <text class="label">患者：</text>
    <text class="value">{{order.patientName}}</text>
  </view>
</view>

<!-- 药品信息卡片（仅药品订单显示） -->
<view class="medicine-info-card" wx:if="{{order.serviceType === 'proxy'}}">
  <view class="card-title">💊 药品信息</view>
  <view class="medicine-list">
    <view class="medicine-item" wx:for="{{order.serviceDetails.items}}" wx:key="index">
      <view class="medicine-name">{{item.name}}</view>
      <view class="medicine-spec">{{item.desc}}</view>
      <view class="medicine-price">
        <text class="price">¥{{item.price}}</text>
        <text class="quantity">x{{item.quantity}}</text>
        <text class="total">¥{{item.price * item.quantity}}</text>
      </view>
    </view>
  </view>
</view>

<!-- 配送信息卡片（仅药品订单显示） -->
<view class="delivery-info-card" wx:if="{{order.serviceType === 'proxy'}}">
  <view class="card-title">🚚 配送信息</view>
  <view class="info-item">
    <text class="label">配送地址：</text>
    <text class="value">{{order.deliveryInfo.address}}</text>
  </view>
  <view class="info-item">
    <text class="label">配送状态：</text>
    <text class="value status-{{medicineOrderStatus}}">{{medicineStatusText}}</text>
  </view>
</view>

<!-- 费用信息卡片 -->
<view class="fee-info-card">
  <view class="card-title">费用信息</view>
  <view class="info-item">
    <text class="label">总价：</text>
    <text class="value price">¥{{order.price}}</text>
  </view>
  <view class="info-item">
    <text class="label">支付状态：</text>
    <text class="value">{{paymentStatusText}}</text>
  </view>
</view>

<!-- 操作按钮区域 -->
<view class="action-buttons">
  <button wx:for="{{actionButtons}}" wx:key="index" 
          class="action-btn {{item.type}}" 
          bindtap="onActionClick" 
          data-action="{{item.action}}">
    {{item.text}}
  </button>
</view>
```

### 6.2 JavaScript逻辑
```javascript
Page({
  data: {
    order: {},
    medicineOrder: null,
    orderStatusText: '',
    medicineStatusText: '',
    actionButtons: []
  },

  async onLoad(options) {
    const orderId = options.id;
    await this.loadOrderDetail(orderId);
    this.updateDisplayData();
    this.updateActionButtons();
  },

  async loadOrderDetail(orderId) {
    const orderDetail = await getOrderDetail(orderId);
    
    // 如果是药品订单，获取药品子订单信息
    if (orderDetail.serviceType === 'proxy') {
      const medicineOrder = await getMedicineOrderByServiceOrder(orderId);
      orderDetail.medicineOrder = medicineOrder;
    }
    
    this.setData({ order: orderDetail });
  },

  updateDisplayData() {
    const { order } = this.data;
    const isMedicineOrder = order.serviceType === 'proxy';
    
    // 更新状态文本
    const orderStatusText = this.getOrderStatusText(order.status);
    let displayStatusText = orderStatusText;
    
    if (isMedicineOrder && order.medicineOrder) {
      const medicineStatusText = this.getMedicineStatusText(order.medicineOrder.status);
      displayStatusText = `${orderStatusText} · ${medicineStatusText}`;
    }
    
    this.setData({
      orderStatusText: displayStatusText,
      medicineStatusText: isMedicineOrder ? this.getMedicineStatusText(order.medicineOrder?.status) : ''
    });
  },

  updateActionButtons() {
    const { order } = this.data;
    const userRole = this.getUserRole();
    const buttons = this.getActionButtons(order, userRole);
    this.setData({ actionButtons: buttons });
  },

  getActionButtons(order, userRole) {
    if (order.serviceType === 'proxy') {
      return getMedicineOrderButtons(order, userRole);
    } else {
      return getMainOrderButtons(order, userRole);
    }
  }
});
```

## 7. 样式设计

### 7.1 卡片通用样式
```css
.order-card {
  background: #fff;
  border-radius: 12px;
  padding: 24rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.08);
}

.card-title {
  font-size: 28rpx;
  font-weight: 700;
  color: #0C4147;
  margin-bottom: 16rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12rpx;
}

.label {
  font-size: 24rpx;
  color: #666;
  min-width: 120rpx;
}

.value {
  font-size: 24rpx;
  color: #333;
  flex: 1;
  text-align: right;
}
```

### 7.2 药品信息样式
```css
.medicine-item {
  display: flex;
  flex-direction: column;
  padding: 16rpx 0;
  border-bottom: 1px solid #f0f0f0;
}

.medicine-name {
  font-size: 26rpx;
  font-weight: 600;
  color: #0C4147;
  margin-bottom: 8rpx;
}

.medicine-spec {
  font-size: 22rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.medicine-price {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.price {
  font-size: 24rpx;
  color: #BA1A1A;
  font-weight: 600;
}

.quantity {
  font-size: 22rpx;
  color: #999;
}

.total {
  font-size: 24rpx;
  color: #BA1A1A;
  font-weight: 600;
}
```

### 7.3 配送状态样式
```css
.status-pending { color: #666; }
.status-preparing { color: #FF9500; }
.status-shipped { color: #007AFF; }
.status-delivered { color: #34C759; }
.status-completed { color: #34C759; }
.status-cancelled { color: #FF3B30; }
```

## 8. 注意事项

### 8.1 数据兼容性
- 确保药品订单的 `serviceDetails.items` 数据结构正确
- 处理 `deliveryInfo` 可能为空的情况
- 兼容不同版本的API返回格式

### 8.2 状态同步
- 主服务订单状态和药品子订单状态需要同步显示
- 状态变更时需要实时更新页面显示
- 操作按钮权限需要根据两个状态综合判断

### 8.3 用户体验
- 药品信息卡片仅在药品订单时显示，避免页面冗余
- 配送信息提供清晰的状态指示
- 操作按钮权限明确，避免用户困惑

### 8.4 性能优化
- 药品子订单信息按需加载
- 图片资源使用懒加载
- 状态更新使用增量更新而非全量刷新

---

## 更新记录

### 2025-01-XX: 初始版本
1. **创建文档**：建立订单详情页设计规范
2. **药品订单特殊逻辑**：详细说明药品订单的显示逻辑
3. **状态显示**：主服务订单和药品子订单状态同步显示
4. **操作按钮**：根据订单类型和用户角色显示相应操作
5. **样式设计**：提供完整的样式规范和实现示例 