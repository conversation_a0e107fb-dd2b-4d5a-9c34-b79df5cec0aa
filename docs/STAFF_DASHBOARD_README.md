# Staff Dashboard 功能说明

## 概述

为staff类型的用户创建了专门的首页（工作台），展示他们的进行中订单和最新评价。

## 功能特性

### 1. 用户信息卡片
- 显示用户头像、姓名和角色
- 显示进行中订单数量和今日评价数量
- 提供个人中心和消息中心的快捷入口

### 2. 进行中订单
- 展示当前staff负责的所有进行中订单
- 显示订单类型、状态、客户信息、下单时间等
- 支持点击查看订单详情
- 提供"查看全部"链接跳转到订单列表

### 3. 最新评价
- 展示与该staff相关的最新10条评价
- 显示评价人、评分、评价内容、标签等
- 支持显示staff的回复内容
- 按时间倒序排列

### 4. 快捷操作
- 我的订单：跳转到订单列表页面
- 消息中心：跳转到消息中心页面
- 个人中心：跳转到个人中心页面

## 技术实现

### 页面结构
```
pages/staff/dashboard/
├── dashboard.js    # 页面逻辑
├── dashboard.wxml  # 页面模板
├── dashboard.wxss  # 页面样式
└── dashboard.json  # 页面配置
```

### 核心功能

#### 1. 自动跳转逻辑
- 在首页（pages/home/<USER>
- 当检测到用户类型为`staff`或`medical_staff`时，自动跳转到dashboard页面
- 使用`wx.redirectTo`确保用户无法返回到普通首页

#### 2. 数据加载
- `loadActiveOrders()`: 加载进行中的订单
- `loadReviews()`: 加载相关评论
- 支持下拉刷新功能

#### 3. API扩展
- 在`api/social.js`中添加了`getReviewsByStaffId()`方法
- 支持直接根据staffId获取相关评论

### 样式设计
- 采用现代化的卡片设计
- 使用渐变背景和阴影效果
- 响应式布局，适配不同屏幕尺寸
- 统一的颜色主题和交互反馈

## 使用流程

1. **登录检测**: 页面加载时检查用户登录状态
2. **用户类型判断**: 验证用户是否为staff类型
3. **数据加载**: 并行加载订单和评论数据
4. **界面渲染**: 根据数据状态显示相应内容
5. **交互响应**: 支持点击跳转和下拉刷新

## 配置说明

### 页面配置 (dashboard.json)
```json
{
  "navigationBarTitleText": "工作台",
  "enablePullDownRefresh": true,
  "backgroundColor": "#f5f7fa",
  "usingComponents": {
    "global-navbar": "/components/global-navbar/index"
  }
}
```

### 路由配置
在`app.json`中添加页面路径：
```json
"pages/staff/dashboard/dashboard"
```

## 注意事项

1. **权限控制**: 只有staff类型的用户才能访问此页面
2. **数据安全**: 通过staffId过滤，确保用户只能看到自己的数据
3. **性能优化**: 使用分页加载，避免一次性加载过多数据
4. **错误处理**: 包含完整的错误处理和用户提示

## 后续优化

1. **实时更新**: 可考虑添加WebSocket支持实时数据更新
2. **数据缓存**: 实现本地缓存机制提升加载速度
3. **更多统计**: 添加更多数据统计和图表展示
4. **个性化**: 支持用户自定义界面布局 