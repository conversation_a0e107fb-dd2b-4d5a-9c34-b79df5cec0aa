# 导航栏精确控制使用指南

## 概述

本指南介绍如何使用精确的导航栏高度控制和内容间距管理功能，确保页面内容与导航栏紧密贴合，避免遮挡或多余空白。

## 功能特性

### 1. 精确高度计算
- 自动计算基于设备状态栏和胶囊按钮的导航栏高度
- 支持自定义固定高度
- 实时响应系统信息变化（如横竖屏切换）

### 2. 内容间距控制
- 精确控制内容区域的顶部间距
- 支持预设间距模式
- 动态调整间距

### 3. 预设配置
- 紧凑模式：最小间距
- 标准模式：适中间距
- 宽松模式：较大间距
- 固定高度模式：使用固定高度

## 使用方法

### 1. 基本使用

#### 在WXML中使用：
```xml
<!-- 全局导航栏 -->
<global-navbar 
  title="页面标题" 
  showBack="{{true}}"
  textColor="#000000"
  backgroundColor="#ffffff"
  customHeight="{{customNavbarHeight}}"
  contentTopMargin="{{contentTopMargin}}"
  bind:navbarReady="onNavbarReady"
></global-navbar>

<!-- 页面内容 -->
<view class="page-container" style="padding-top: {{contentTopPosition}}px;">
  <!-- 页面内容 -->
</view>
```

#### 在JS中使用：
```javascript
import navbarBehavior from '../../behaviors/navbar';
import { createPreciseNavbarConfig, NAVBAR_PRESETS } from '../../utils/navbar.js';

Page({
  behaviors: [navbarBehavior],
  
  data: {
    customNavbarHeight: 0,
    contentTopMargin: 20,
    contentTopPosition: 0
  },
  
  onLoad() {
    this.initPreciseNavbar();
  },
  
  // 初始化精确导航栏配置
  initPreciseNavbar() {
    const navbarConfig = createPreciseNavbarConfig({
      preset: 'standard',
      customHeight: 0,
      contentMargin: 20
    });
    
    this.setData({
      customNavbarHeight: navbarConfig.customHeight,
      contentTopMargin: navbarConfig.contentTopMargin,
      contentTopPosition: navbarConfig.contentTopMargin
    });
  },
  
  // 处理导航栏准备完成事件
  onNavbarReady(e) {
    const { navbarHeight, contentTop, statusBarHeight } = e.detail;
    const contentTopPosition = navbarHeight + this.data.contentTopMargin;
    
    this.setData({
      navbarHeight,
      contentTop,
      statusBarHeight,
      contentTopPosition
    });
  }
});
```

### 2. 预设配置使用

#### 紧凑模式（最小间距）：
```javascript
const navbarConfig = createPreciseNavbarConfig({
  preset: 'compact'
});
```

#### 标准模式（适中间距）：
```javascript
const navbarConfig = createPreciseNavbarConfig({
  preset: 'standard'
});
```

#### 宽松模式（较大间距）：
```javascript
const navbarConfig = createPreciseNavbarConfig({
  preset: 'spacious'
});
```

#### 固定高度模式：
```javascript
const navbarConfig = createPreciseNavbarConfig({
  preset: 'fixed'
});
```

### 3. 自定义配置

#### 设置固定高度：
```javascript
const navbarConfig = createPreciseNavbarConfig({
  customHeight: 88, // 固定88px高度
  contentMargin: 0
});
```

#### 设置自定义间距：
```javascript
const navbarConfig = createPreciseNavbarConfig({
  contentMargin: 30 // 30px的顶部间距
});
```

### 4. 动态调整

#### 动态调整导航栏高度：
```javascript
// 在页面中调用
this.adjustNavbarHeight(100); // 设置为100px高度
```

#### 动态调整内容间距：
```javascript
// 在页面中调用
this.adjustContentMargin(40); // 设置为40px间距
```

#### 动态切换预设：
```javascript
// 在页面中调用
this.useNavbarPreset('compact'); // 切换到紧凑模式
```

## 预设配置详情

### NAVBAR_PRESETS 配置：

```javascript
const NAVBAR_PRESETS = {
  // 紧凑模式：最小间距
  compact: {
    height: 0, // 使用自动计算
    contentMargin: 0
  },
  // 标准模式：适中间距
  standard: {
    height: 0, // 使用自动计算
    contentMargin: 20
  },
  // 宽松模式：较大间距
  spacious: {
    height: 0, // 使用自动计算
    contentMargin: 40
  },
  // 固定高度模式：使用固定高度
  fixed: {
    height: 88, // 固定88px高度
    contentMargin: 0
  }
};
```

## 最佳实践

### 1. 选择合适的预设
- **紧凑模式**：适用于内容密集的页面，如列表页
- **标准模式**：适用于大多数页面，提供良好的视觉层次
- **宽松模式**：适用于重要内容页面，提供更好的可读性
- **固定高度**：适用于需要精确控制的特殊页面

### 2. 响应式适配
```javascript
// 监听系统信息变化
import { watchNavbarHeight } from '../../utils/navbar.js';

watchNavbarHeight((info) => {
  console.log('导航栏高度变化:', info);
  // 重新计算内容位置
  this.setData({
    contentTopPosition: info.navbarHeight + this.data.contentTopMargin
  });
});
```

### 3. 性能优化
- 避免频繁调整导航栏高度
- 使用预设配置而不是频繁计算
- 在页面加载时一次性设置好配置

## 常见问题

### Q: 如何确保内容不被导航栏遮挡？
A: 使用 `contentTopPosition` 作为页面的 `padding-top`，它会自动计算导航栏高度加上间距。

### Q: 如何在不同设备上保持一致的效果？
A: 使用自动计算模式（`customHeight: 0`），系统会自动适配不同设备的状态栏高度。

### Q: 如何动态调整间距？
A: 使用 `adjustContentMargin()` 方法，或者直接修改 `contentTopMargin` 数据。

### Q: 如何处理横竖屏切换？
A: 使用 `watchNavbarHeight()` 监听系统信息变化，自动重新计算位置。

## 示例页面

参考 `pages/orders/orders.js` 和 `pages/orders/orders.wxml` 的完整实现示例。 